# MSI Atlas Converter

一个用于将质谱成像(MSI)数据与Atlas图像进行配准和转换的GUI应用程序。

## 功能特性

- **直观的图形界面**: 左侧工具栏配置，右侧双视图显示
- **多格式支持**: 支持imzML、mzML等质谱成像文件格式
- **图像格式兼容**: 支持PNG、JPG、JPEG格式的Atlas图像
- **多种降采样方法**:
  - Nearest Neighbor (最近邻)
  - Mode Downsampling (众数降采样)
  - Nearest-Neighbor-based Mode Downsampling (基于最近邻的众数降采样)
- **实时预览**: 输入图像和生成结果的实时显示
- **导出功能**: 支持CSV文件和图像文件的下载

## 安装要求

### Python版本
- Python 3.7 或更高版本

### 依赖包
安装所需的Python包：

```bash
pip install -r requirements.txt
```

主要依赖包包括：
- tkinter (GUI框架)
- numpy (数值计算)
- pandas (数据处理)
- Pillow (图像处理)
- opencv-python (计算机视觉)
- scipy (科学计算)
- matplotlib (图表绘制)
- pyimzml (质谱成像文件处理)

## 使用方法

### 启动程序

```bash
python main.py
```

### 操作步骤

1. **选择质谱成像文件**
   - 点击"选择MSI文件"按钮
   - 选择支持的质谱成像文件(.imzML, .mzML)
   - 程序会自动读取并显示文件的分辨率信息

2. **选择Atlas图像文件**
   - 点击"选择Atlas文件"按钮
   - 选择PNG、JPG或JPEG格式的图像文件
   - 选中的图像会在右侧上方视图框中显示

3. **选择降采样方法**
   - **Nearest Neighbor**: 最近邻插值，速度快，适合保持边缘清晰
   - **Mode Downsampling**: 众数降采样，适合分类图像，保持区域一致性
   - **Nearest-Neighbor-based Mode Downsampling**: 结合两种方法的优势

4. **生成CSV文件**
   - 点击"生成CSV文件"按钮
   - 程序会根据MSI分辨率对Atlas图像进行降采样
   - 生成的像素级CSV文件对应的图像会在右侧下方视图框中显示

5. **下载结果**
   - 点击"下载CSV文件"保存像素级数据
   - 点击"下载图像文件"保存生成的图像

## 界面布局

```
┌─────────────────┬─────────────────────────────┐
│                 │        输入图像显示          │
│   左侧工具栏     │                            │
│                 ├─────────────────────────────┤
│  - MSI文件输入   │        生成图像显示          │
│  - Atlas文件输入 │                            │
│  - 降采样方法    │                            │
│  - 生成按钮     │                            │
│  - 下载按钮     │                            │
└─────────────────┴─────────────────────────────┘
```

## 技术说明

### 降采样算法

1. **Nearest Neighbor**: 使用OpenCV的INTER_NEAREST插值方法
2. **Mode Downsampling**: 将原图像分块，每块取众数值
3. **NN-based Mode**: 先最近邻降采样，再在3x3邻域内应用众数滤波

### 文件格式支持

- **输入**: imzML, mzML (质谱成像), PNG, JPG, JPEG (Atlas图像)
- **输出**: CSV (像素数据), PNG/JPG (图像文件)

## 注意事项

1. 确保MSI文件和Atlas图像在空间上对应
2. 大尺寸图像处理可能需要较长时间
3. 生成的CSV文件大小取决于MSI数据的分辨率
4. 建议在处理前备份原始数据

## 故障排除

### 分辨率识别问题

**v2.1更新：** 大幅改进了分辨率读取功能的稳定性和用户体验

**主要改进：**
- ✅ 增强错误处理和诊断信息
- ✅ 大文件自动优化处理
- ✅ 详细的用户友好错误提示
- ✅ 文件完整性自动检查
- ✅ 性能监控和日志记录

**问题**: "分辨率识别失败"

**常见原因：**
- 缺少`pyimzml`库
- imzML文件格式问题或损坏
- 文件路径问题或权限不足
- 大文件处理需要时间
- 缺少配套的.ibd文件

**解决方案：**
1. **安装依赖库：**
   ```bash
   pip install pyimzml
   # 或使用conda
   conda install -c bioconda pyimzml
   ```

2. **文件检查：**
   - 确保.imzML和.ibd文件都存在
   - 验证文件下载完整
   - 检查文件权限

3. **使用诊断工具：**
   ```bash
   python test_resolution_improvements.py
   ```

4. **查看详细指南：**
   - `故障排除指南.md` - 详细的问题解决方案
   - `分辨率读取最佳实践.md` - 性能优化和最佳实践

### 其他常见问题

1. **程序启动失败**
   - 检查Python版本（需要3.7+）
   - 确认所有依赖包已安装
   - 运行测试脚本: `python test_msi_reader.py`

2. **图像显示异常**
   - 调整窗口大小
   - 检查图像文件格式
   - 确认PIL库正常工作

3. **CSV生成失败**
   - 检查输入文件是否有效
   - 确认有足够的磁盘空间
   - 检查文件权限

4. **处理速度慢**
   - 尝试较小的图像文件
   - 选择"Nearest Neighbor"方法
   - 关闭其他占用内存的程序

## 开发信息

- **开发语言**: Python
- **GUI框架**: tkinter
- **图像处理**: OpenCV, Pillow
- **数据处理**: NumPy, Pandas

## 许可证

本项目采用MIT许可证。详见LICENSE文件。