#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化的分辨率读取功能
验证新功能是否正常工作
"""

import os
import sys
import time
import tempfile
from pathlib import Path

def create_simple_imzml(width: int, height: int, output_path: str) -> bool:
    """
    创建简单的测试imzML文件
    
    Args:
        width: 图像宽度
        height: 图像高度
        output_path: 输出文件路径
        
    Returns:
        bool: 是否创建成功
    """
    try:
        # 创建符合pyimzml要求的imzML文件内容
        imzml_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<mzML xmlns="http://psi.hupo.org/ms/mzml" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://psi.hupo.org/ms/mzml http://psidev.info/files/ms/mzML/xsd/mzML1.1.0.xsd" version="1.1.0">
  <cvList count="2">
    <cv id="MS" fullName="Proteomics Standards Initiative Mass Spectrometry Ontology" version="4.1.0" URI="https://raw.githubusercontent.com/HUPO-PSI/psi-ms-CV/master/psi-ms.obo"/>
    <cv id="IMS" fullName="Imaging MS Ontology" version="1.1.0" URI="https://raw.githubusercontent.com/HUPO-PSI/psi-ms-CV/master/psi-ms.obo"/>
  </cvList>
  <fileDescription>
    <fileContent>
      <cvParam cvRef="MS" accession="MS:1000579" name="MS1 spectrum"/>
      <cvParam cvRef="IMS" accession="IMS:1000080" name="universally unique identifier"/>
      <cvParam cvRef="IMS" accession="IMS:1000031" name="processed"/>
    </fileContent>
  </fileDescription>
  <referenceableParamGroupList count="1">
    <referenceableParamGroup id="mzArray">
      <cvParam cvRef="MS" accession="MS:1000514" name="m/z array" unitCvRef="MS" unitAccession="MS:1000040" unitName="m/z"/>
      <cvParam cvRef="MS" accession="MS:1000523" name="64-bit float"/>
      <cvParam cvRef="MS" accession="MS:1000576" name="no compression"/>
    </referenceableParamGroup>
  </referenceableParamGroupList>
  <run id="test_run">
    <spectrumList count="{width * height}">'''
        
        # 添加光谱数据
        spectrum_id = 1
        for y in range(1, height + 1):
            for x in range(1, width + 1):
                imzml_content += f'''
      <spectrum id="spectrum_{spectrum_id}" index="{spectrum_id - 1}" defaultArrayLength="0">
        <cvParam cvRef="MS" accession="MS:1000127" name="centroid spectrum"/>
        <cvParam cvRef="MS" accession="MS:1000511" name="ms level" value="1"/>
        <scanList count="1">
          <cvParam cvRef="MS" accession="MS:1000795" name="no combination"/>
          <scan>
            <cvParam cvRef="IMS" accession="IMS:1000050" name="position x" value="{x}"/>
            <cvParam cvRef="IMS" accession="IMS:1000051" name="position y" value="{y}"/>
            <cvParam cvRef="IMS" accession="IMS:1000052" name="position z" value="1"/>
          </scan>
        </scanList>
        <binaryDataArrayList count="2">
          <binaryDataArray encodedLength="0">
            <referenceableParamGroupRef ref="mzArray"/>
            <binary></binary>
          </binaryDataArray>
          <binaryDataArray encodedLength="0">
            <cvParam cvRef="MS" accession="MS:1000515" name="intensity array" unitCvRef="MS" unitAccession="MS:1000131" unitName="number of detector counts"/>
            <cvParam cvRef="MS" accession="MS:1000523" name="64-bit float"/>
            <cvParam cvRef="MS" accession="MS:1000576" name="no compression"/>
            <binary></binary>
          </binaryDataArray>
        </binaryDataArrayList>
      </spectrum>'''
                spectrum_id += 1
        
        imzml_content += '''    </spectrumList>
  </run>
</mzML>'''
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(imzml_content)
        
        # 创建对应的.ibd文件（空文件）
        ibd_path = output_path.replace('.imzML', '.ibd')
        with open(ibd_path, 'wb') as f:
            f.write(b'')  # 空的二进制文件
        
        print(f"✓ 创建测试文件: {output_path} ({width}x{height})")
        return True
        
    except Exception as e:
        print(f"✗ 创建测试文件失败: {e}")
        return False

def test_optimized_reader():
    """测试优化的分辨率读取器"""
    print("=" * 60)
    print("测试优化的MSI分辨率读取功能")
    print("=" * 60)
    
    # 检查依赖
    print("\n1. 检查依赖库...")
    try:
        import pyimzml
        print("✓ pyimzml 已安装")
    except ImportError:
        print("✗ pyimzml 未安装，请运行: pip install pyimzml")
        return False
    
    try:
        from optimized_msi_reader import get_optimized_msi_resolution, get_msi_cache_info, clear_msi_cache
        print("✓ 优化读取器模块已导入")
    except ImportError as e:
        print(f"✗ 优化读取器模块导入失败: {e}")
        return False
    
    # 创建临时测试文件
    print("\n2. 创建测试文件...")
    temp_dir = tempfile.mkdtemp()
    test_cases = [
        (10, 10, "小文件"),
        (32, 32, "中等文件"),
        (64, 48, "不规则尺寸")
    ]
    
    test_files = []
    for width, height, desc in test_cases:
        test_file = os.path.join(temp_dir, f"test_{width}x{height}.imzML")
        if create_simple_imzml(width, height, test_file):
            test_files.append((test_file, width, height, desc))
    
    if not test_files:
        print("✗ 无法创建测试文件")
        return False
    
    # 测试优化读取器
    print("\n3. 测试优化分辨率读取...")
    
    success_count = 0
    total_count = len(test_files)
    
    for test_file, expected_width, expected_height, desc in test_files:
        print(f"\n测试 {desc} ({expected_width}x{expected_height}):")
        
        # 进度回调
        progress_values = []
        def progress_callback(progress):
            progress_values.append(progress)
            if len(progress_values) % 5 == 0:  # 每5次更新显示一次
                print(f"  进度: {progress:.1%}")
        
        try:
            start_time = time.time()
            
            # 测试优化读取器
            resolution_info = get_optimized_msi_resolution(test_file, progress_callback)
            
            end_time = time.time()
            
            if resolution_info:
                width, height = resolution_info.width, resolution_info.height
                
                print(f"  ✓ 读取成功: {width}x{height}")
                print(f"  ✓ 耗时: {end_time - start_time:.3f}秒")
                print(f"  ✓ 总点数: {resolution_info.total_points:,}")
                print(f"  ✓ 网格密度: {resolution_info.grid_density:.1%}")
                print(f"  ✓ 规则网格: {'是' if resolution_info.is_regular_grid else '否'}")
                print(f"  ✓ 置信度: {resolution_info.confidence:.1%}")
                print(f"  ✓ 进度更新次数: {len(progress_values)}")
                
                # 验证结果
                if width == expected_width and height == expected_height:
                    print(f"  ✓ 分辨率正确")
                    success_count += 1
                else:
                    print(f"  ✗ 分辨率错误，期望 {expected_width}x{expected_height}，得到 {width}x{height}")
            else:
                print(f"  ✗ 读取失败")
                
        except Exception as e:
            print(f"  ✗ 读取异常: {str(e)}")
    
    # 测试缓存功能
    print("\n4. 测试缓存功能...")
    try:
        cache_info = get_msi_cache_info()
        print(f"✓ 缓存信息获取成功:")
        print(f"  内存缓存: {cache_info['memory_cache_count']} 项")
        print(f"  磁盘缓存: {cache_info['disk_cache_count']} 项")
        print(f"  缓存目录: {cache_info['cache_dir']}")
        
        # 测试缓存读取（重复读取第一个文件）
        if test_files:
            test_file, _, _, _ = test_files[0]
            print(f"\n测试缓存读取...")
            
            start_time = time.time()
            resolution_info = get_optimized_msi_resolution(test_file)
            end_time = time.time()
            
            if resolution_info:
                print(f"✓ 缓存读取成功，耗时: {end_time - start_time:.3f}秒")
            else:
                print(f"✗ 缓存读取失败")
        
    except Exception as e:
        print(f"✗ 缓存功能测试失败: {str(e)}")
    
    # 清理测试文件
    print("\n5. 清理测试文件...")
    try:
        import shutil
        shutil.rmtree(temp_dir)
        print("✓ 测试文件已清理")
    except Exception as e:
        print(f"⚠ 清理测试文件失败: {e}")
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    print(f"成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count == total_count:
        print("✓ 所有测试通过！优化的分辨率读取功能正常工作")
        return True
    else:
        print(f"✗ {total_count - success_count} 个测试失败")
        return False

def test_fallback_mechanism():
    """测试回退机制"""
    print("\n" + "=" * 60)
    print("测试回退机制")
    print("=" * 60)
    
    try:
        from optimized_msi_reader import get_optimized_msi_resolution
        
        # 测试不存在的文件
        print("\n测试不存在的文件...")
        try:
            result = get_optimized_msi_resolution("nonexistent_file.imzML")
            print(f"✗ 应该抛出异常，但返回了: {result}")
        except FileNotFoundError:
            print("✓ 正确处理不存在的文件")
        except Exception as e:
            print(f"✓ 正确抛出异常: {type(e).__name__}: {e}")
        
        # 测试不支持的格式
        print("\n测试不支持的格式...")
        temp_file = tempfile.NamedTemporaryFile(suffix=".xyz", delete=False)
        temp_file.write(b"dummy content")
        temp_file.close()
        
        try:
            result = get_optimized_msi_resolution(temp_file.name)
            if result:
                print(f"✓ 不支持格式使用默认值: {result.width}x{result.height}")
                print(f"  置信度: {result.confidence:.1%}")
            else:
                print("✗ 不支持格式返回None")
        except Exception as e:
            print(f"✓ 不支持格式正确抛出异常: {e}")
        finally:
            os.unlink(temp_file.name)
        
        print("✓ 回退机制测试完成")
        
    except Exception as e:
        print(f"✗ 回退机制测试失败: {e}")

def main():
    """主函数"""
    print("MSI分辨率读取优化功能测试")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 运行主要测试
    success = test_optimized_reader()
    
    # 运行回退机制测试
    test_fallback_mechanism()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 优化功能测试通过！")
        print("\n建议:")
        print("• 在实际使用中测试真实的imzML文件")
        print("• 监控缓存使用情况")
        print("• 关注置信度评估结果")
        print("• 利用进度回调改善用户体验")
    else:
        print("❌ 部分测试失败，请检查实现")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()