#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分辨率读取功能改进测试脚本
用于验证MSI文件分辨率读取的错误处理和用户体验改进
"""

import os
import sys
import logging
from msi_reader import get_msi_resolution, MSIReader

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_error_handling():
    """测试各种错误情况的处理"""
    print("\n=== 错误处理测试 ===")
    
    # 测试1: 不存在的文件
    print("\n1. 测试不存在的文件:")
    result = get_msi_resolution("nonexistent_file.imzml")
    print(f"   结果: {result}")
    
    # 测试2: 空文件
    print("\n2. 测试空文件:")
    empty_file = "empty_test.imzml"
    try:
        with open(empty_file, 'w') as f:
            pass  # 创建空文件
        result = get_msi_resolution(empty_file)
        print(f"   结果: {result}")
    finally:
        if os.path.exists(empty_file):
            os.remove(empty_file)
    
    # 测试3: 无效格式文件
    print("\n3. 测试无效格式文件:")
    invalid_file = "invalid_test.imzml"
    try:
        with open(invalid_file, 'w') as f:
            f.write("这不是一个有效的imzML文件")
        result = get_msi_resolution(invalid_file)
        print(f"   结果: {result}")
    finally:
        if os.path.exists(invalid_file):
            os.remove(invalid_file)
    
    # 测试4: 不支持的文件格式
    print("\n4. 测试不支持的文件格式:")
    result = get_msi_resolution("test.txt")
    print(f"   结果: {result}")

def test_resolution_validation():
    """测试分辨率验证功能"""
    print("\n=== 分辨率验证测试 ===")
    
    test_cases = [
        (64, 64, True, "正常分辨率"),
        (0, 64, False, "宽度为0"),
        (64, 0, False, "高度为0"),
        (-10, 64, False, "负数宽度"),
        (64, -10, False, "负数高度"),
        (15000, 64, False, "过大宽度"),
        (64, 15000, False, "过大高度"),
        (1, 1, True, "最小有效分辨率"),
        (9999, 9999, True, "最大有效分辨率")
    ]
    
    for width, height, expected, description in test_cases:
        result = MSIReader.validate_resolution((width, height))
        status = "✓" if result == expected else "✗"
        print(f"   {status} {description}: ({width}, {height}) -> {result}")

def test_file_size_handling():
    """测试文件大小处理"""
    print("\n=== 文件大小处理测试 ===")
    
    # 创建不同大小的测试文件
    test_files = [
        ("small_test.imzml", 1024, "小文件"),
        ("medium_test.imzml", 1024 * 1024, "中等文件"),
        ("large_test.imzml", 10 * 1024 * 1024, "大文件")
    ]
    
    for filename, size, description in test_files:
        print(f"\n测试{description} ({size / 1024 / 1024:.1f}MB):")
        try:
            # 创建指定大小的文件
            with open(filename, 'wb') as f:
                f.write(b'\x00' * size)
            
            # 测试文件大小检测
            file_size = os.path.getsize(filename)
            print(f"   文件大小: {file_size / 1024 / 1024:.1f}MB")
            
            # 尝试读取分辨率（会失败，但测试大小检测逻辑）
            result = get_msi_resolution(filename)
            print(f"   读取结果: {result}")
            
        finally:
            if os.path.exists(filename):
                os.remove(filename)

def test_dependency_check():
    """测试依赖库检查"""
    print("\n=== 依赖库检查 ===")
    
    try:
        import pyimzml
        print("   ✓ pyimzml库已安装")
        print(f"   版本: {pyimzml.__version__ if hasattr(pyimzml, '__version__') else '未知'}")
    except ImportError:
        print("   ✗ pyimzml库未安装")
        print("   建议: pip install pyimzml")
    
    # 检查其他可能的依赖
    dependencies = ['numpy', 'lxml']
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"   ✓ {dep}库已安装")
        except ImportError:
            print(f"   ✗ {dep}库未安装")

def print_improvement_summary():
    """打印改进总结"""
    print("\n" + "="*60)
    print("分辨率读取功能改进总结")
    print("="*60)
    
    improvements = [
        "1. 增强错误处理 - 提供详细的错误信息和解决建议",
        "2. 文件大小检测 - 对大文件提供警告和优化处理",
        "3. 坐标读取优化 - 更安全的坐标数据读取方式",
        "4. 分辨率验证 - 验证读取结果的合理性",
        "5. 日志记录增强 - 提供详细的处理过程日志",
        "6. 用户友好提示 - 针对不同错误类型提供具体解决方案",
        "7. 依赖库检查 - 更好的依赖库缺失处理",
        "8. 文件格式支持 - 改进的imzML文件解析"
    ]
    
    for improvement in improvements:
        print(f"   {improvement}")
    
    print("\n建议:")
    print("   • 使用真实的imzML文件测试完整功能")
    print("   • 确保pyimzml库已正确安装")
    print("   • 查看故障排除指南.md获取更多帮助")
    print("   • 对于大文件，耐心等待处理完成")

def main():
    """主测试函数"""
    print("MSI Atlas Converter - 分辨率读取功能改进测试")
    print("="*60)
    
    try:
        # 运行各项测试
        test_dependency_check()
        test_error_handling()
        test_resolution_validation()
        test_file_size_handling()
        
        # 打印改进总结
        print_improvement_summary()
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return 1
    
    print("\n测试完成！")
    return 0

if __name__ == "__main__":
    sys.exit(main())