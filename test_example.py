#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试示例脚本
用于验证MSI Atlas Converter的核心功能
"""

import numpy as np
import pandas as pd
from PIL import Image
import cv2
from scipy import stats
import os

def create_test_atlas_image():
    """创建一个测试用的Atlas图像"""
    # 创建一个简单的测试图像 (200x200)
    image = np.zeros((200, 200, 3), dtype=np.uint8)
    
    # 添加一些彩色区域
    image[50:100, 50:100] = [255, 0, 0]  # 红色方块
    image[120:170, 120:170] = [0, 255, 0]  # 绿色方块
    image[30:80, 120:170] = [0, 0, 255]  # 蓝色方块
    image[120:170, 30:80] = [255, 255, 0]  # 黄色方块
    
    # 保存测试图像
    test_image = Image.fromarray(image)
    test_image.save('test_data/test_atlas.png')
    print("测试Atlas图像已创建: test_data/test_atlas.png")
    return test_image

def simulate_msi_resolution():
    """模拟MSI分辨率"""
    return (64, 64)

def test_downsampling_methods():
    """测试所有降采样方法（RGB版本）"""
    # 确保test_data目录存在
    os.makedirs('test_data', exist_ok=True)
    
    # 创建测试图像
    test_image = create_test_atlas_image()
    atlas_array = np.array(test_image)
    
    # 保持RGB格式
    if len(atlas_array.shape) == 2:
        # 如果是灰度图，转换为RGB
        atlas_array = np.stack([atlas_array] * 3, axis=-1)
    elif atlas_array.shape[2] == 4:
        # 如果是RGBA，转换为RGB
        atlas_array = atlas_array[:, :, :3]
    
    target_width, target_height = simulate_msi_resolution()
    
    print(f"原始图像尺寸: {atlas_array.shape}")
    print(f"目标分辨率: {target_width} x {target_height}")
    
    # 测试三种降采样方法（RGB版本）
    methods = {
        'nearest_neighbor_rgb': nearest_neighbor_downsampling_rgb,
        'mode_downsampling_rgb': mode_downsampling_rgb,
        'nn_mode_downsampling_rgb': nn_mode_downsampling_rgb
    }
    
    for method_name, method_func in methods.items():
        print(f"\n测试 {method_name}...")
        try:
            result = method_func(atlas_array, target_width, target_height)
            
            # 生成RGB CSV数据
            csv_data = generate_rgb_csv_data(result)
            csv_filename = f'test_data/{method_name}_result.csv'
            csv_data.to_csv(csv_filename, index=False, header=True)
            
            # 保存结果为图像
            img_filename = f'test_data/{method_name}_result.png'
            result_image = Image.fromarray(result.astype(np.uint8))
            # 放大以便查看
            result_image_large = result_image.resize((200, 200), Image.Resampling.NEAREST)
            result_image_large.save(img_filename)
            
            print(f"  结果尺寸: {result.shape}")
            print(f"  CSV文件: {csv_filename} (包含 {len(csv_data)} 个像素点)")
            print(f"  图像文件: {img_filename}")
            print(f"  RGB值范围: R({result[:,:,0].min()}-{result[:,:,0].max()}) G({result[:,:,1].min()}-{result[:,:,1].max()}) B({result[:,:,2].min()}-{result[:,:,2].max()})")
            
        except Exception as e:
            print(f"  错误: {str(e)}")

def generate_rgb_csv_data(rgb_array):
    """生成包含RGB颜色信息的CSV数据"""
    height, width, channels = rgb_array.shape
    
    # 创建包含坐标和RGB值的数据列表
    csv_data = []
    
    for i in range(height):
        for j in range(width):
            r, g, b = rgb_array[i, j]
            csv_data.append({
                'X': j,
                'Y': i,
                'R': int(r),
                'G': int(g),
                'B': int(b),
                'RGB_Hex': f'#{int(r):02x}{int(g):02x}{int(b):02x}'
            })
    
    return pd.DataFrame(csv_data)

def nearest_neighbor_downsampling_rgb(image, target_width, target_height):
    """RGB最近邻降采样"""
    return cv2.resize(image, (target_width, target_height), interpolation=cv2.INTER_NEAREST)

def mode_downsampling_rgb(image, target_width, target_height):
    """RGB众数降采样"""
    original_height, original_width, channels = image.shape
    
    # 计算每个目标像素对应的原始图像区域大小
    block_height = original_height / target_height
    block_width = original_width / target_width
    
    result = np.zeros((target_height, target_width, channels), dtype=image.dtype)
    
    for i in range(target_height):
        for j in range(target_width):
            # 计算对应的原始图像区域
            start_y = int(i * block_height)
            end_y = int((i + 1) * block_height)
            start_x = int(j * block_width)
            end_x = int((j + 1) * block_width)
            
            # 对每个颜色通道分别计算众数
            for c in range(channels):
                block = image[start_y:end_y, start_x:end_x, c]
                if block.size > 0:
                    mode_result = stats.mode(block.flatten(), keepdims=True)
                    result[i, j, c] = mode_result[0][0]
            
    return result

def nn_mode_downsampling_rgb(image, target_width, target_height):
    """基于最近邻的RGB众数降采样"""
    # 先进行最近邻降采样
    nn_result = nearest_neighbor_downsampling_rgb(image, target_width, target_height)
    
    # 然后在小邻域内应用众数滤波
    result = np.copy(nn_result)
    channels = nn_result.shape[2]
    
    for i in range(1, target_height-1):
        for j in range(1, target_width-1):
            for c in range(channels):
                neighborhood = nn_result[i-1:i+2, j-1:j+2, c]
                if neighborhood.size > 0:
                    mode_result = stats.mode(neighborhood.flatten(), keepdims=True)
                    result[i, j, c] = mode_result[0][0]
                
    return result

def main():
    """主测试函数"""
    print("MSI Atlas Converter 测试脚本")
    print("=" * 40)
    
    try:
        test_downsampling_methods()
        print("\n测试完成！请检查 test_data 目录中的结果文件。")
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()