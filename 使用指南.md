# MSI Atlas Converter 使用指南

## 快速开始

### 1. 环境准备

确保已安装Python 3.7+，然后安装依赖包：

```bash
pip install -r requirements.txt
```

### 2. 启动程序

```bash
python main.py
```

程序启动后，您将看到一个包含左侧工具栏和右侧双视图的界面。

## 详细操作步骤

### 步骤1：选择质谱成像文件

1. 在左侧工具栏的"质谱成像文件"区域
2. 点击"选择MSI文件"按钮
3. 在文件对话框中选择您的MSI文件（支持.imzML, .mzML格式）
4. 选择后，程序会自动读取并显示文件的分辨率信息

**注意**：当前版本为演示版本，MSI分辨率设置为固定的64x64。在实际应用中，程序会从真实的MSI文件中读取分辨率。

### 步骤2：选择Atlas图像文件

1. 在"Atlas图像文件"区域
2. 点击"选择Atlas文件"按钮
3. 选择PNG、JPG或JPEG格式的图像文件
4. 选中的图像会立即在右侧上方的"输入图像"视图框中显示

### 步骤3：选择降采样方法

在"降采样方法"区域，选择以下三种方法之一：

#### Nearest Neighbor（最近邻）
- **特点**：速度最快，计算简单
- **适用场景**：需要保持图像边缘清晰度的情况
- **原理**：直接选择最近的像素值，保持RGB颜色信息

#### Mode Downsampling（众数降采样）
- **特点**：保持区域一致性，适合分类图像
- **适用场景**：Atlas图像包含明确区域分割的情况
- **原理**：将原图像分块，每个RGB通道分别取众数值作为结果

#### Nearest-Neighbor-based Mode Downsampling（基于最近邻的众数降采样）
- **特点**：结合两种方法的优势
- **适用场景**：需要平衡速度和质量的情况
- **原理**：先进行最近邻降采样，再在3x3邻域内对每个RGB通道应用众数滤波

### 步骤4：生成CSV文件

1. 确保已选择MSI文件和Atlas图像文件
2. 点击"生成CSV文件"按钮
3. 程序会根据选择的降采样方法处理图像，**保留完整的RGB颜色信息**
4. 处理完成后：
   - 右侧下方的"生成图像"视图框会显示结果
   - 下载按钮会被激活
   - 弹出成功提示，显示像素点数量

### 步骤5：下载结果

#### 下载RGB CSV文件
1. 点击"下载CSV文件"按钮
2. 在保存对话框中选择保存位置和文件名
3. **CSV文件包含完整的RGB颜色信息**，格式如下：
   - **X**: 像素的X坐标（列）
   - **Y**: 像素的Y坐标（行）
   - **R**: 红色通道值（0-255）
   - **G**: 绿色通道值（0-255）
   - **B**: 蓝色通道值（0-255）
   - **RGB_Hex**: 十六进制颜色代码（如#FF0000）

#### 下载图像文件
1. 点击"下载图像文件"按钮
2. 选择保存格式（PNG推荐，JPEG可选）
3. 图像文件是CSV数据的可视化表示，保持原始RGB颜色

## CSV文件格式说明

### 新的RGB CSV格式

生成的CSV文件现在包含完整的RGB颜色信息：

```csv
X,Y,R,G,B,RGB_Hex
0,0,255,0,0,#ff0000
1,0,255,0,0,#ff0000
2,0,0,255,0,#00ff00
3,0,0,0,255,#0000ff
...
```

### 数据说明

- **坐标系统**：左上角为原点(0,0)，X轴向右，Y轴向下
- **颜色值**：RGB值范围为0-255
- **十六进制颜色**：便于在其他软件中使用的标准颜色格式
- **数据量**：对于64x64分辨率，包含4096个像素点的颜色信息

## 界面布局

```
┌─────────────────┬─────────────────────────────┐
│                 │        输入图像显示          │
│   左侧工具栏     │      (保持原始RGB颜色)       │
│                 ├─────────────────────────────┤
│  - MSI文件输入   │        生成图像显示          │
│  - Atlas文件输入 │      (降采样后RGB图像)       │
│  - 降采样方法    │                            │
│  - 生成按钮     │                            │
│  - 下载按钮     │                            │
└─────────────────┴─────────────────────────────┘
```

## 技术说明

### RGB颜色处理

1. **输入处理**：
   - 自动检测图像格式（灰度/RGB/RGBA）
   - 灰度图像自动转换为RGB格式
   - RGBA图像去除透明通道，保留RGB

2. **降采样算法**：
   - **Nearest Neighbor**: 对RGB图像直接进行最近邻插值
   - **Mode Downsampling**: 对R、G、B三个通道分别计算众数
   - **NN-based Mode**: 先最近邻降采样，再对每个通道进行众数滤波

3. **数据输出**：
   - CSV包含像素坐标和完整RGB信息
   - 支持十六进制颜色代码
   - 保持颜色精度和一致性

### 文件格式支持

- **输入**: imzML, mzML (质谱成像), PNG, JPG, JPEG (Atlas图像)
- **输出**: CSV (RGB像素数据), PNG/JPG (RGB图像文件)

### 性能考虑

- **内存使用**：RGB处理比灰度处理占用更多内存
- **处理时间**：RGB众数降采样需要更长时间
- **文件大小**：RGB CSV文件比灰度版本大约3倍

## 测试功能

程序包含一个RGB测试脚本，可以验证核心功能：

```bash
python test_example.py
```

测试脚本会：
1. 创建一个彩色测试用的Atlas图像
2. 使用三种降采样方法处理RGB图像
3. 生成包含RGB信息的CSV和图像文件保存在`test_data`目录
4. 显示每种方法的RGB值范围和像素点数量

## 常见问题

### Q: CSV文件很大，如何处理？
A: RGB CSV文件包含更多信息，因此较大。可以：
- 使用压缩软件减小文件大小
- 在Excel等软件中按需筛选数据
- 使用编程方式处理大文件

### Q: 如何在其他软件中使用RGB数据？
A: 
- 使用X、Y坐标重建图像
- 利用RGB_Hex列在设计软件中应用颜色
- 分析R、G、B数值进行颜色统计

### Q: 为什么RGB值和原图不完全一致？
A: 这是正常的，因为：
- 降采样过程会改变像素值
- 众数算法选择区域内最常见的颜色
- 图像压缩可能影响原始颜色

### Q: 如何选择最适合的降采样方法？
A:
- **保持细节**：选择Nearest Neighbor
- **区域一致性**：选择Mode Downsampling
- **平衡效果**：选择NN-based Mode Downsampling

## 数据应用示例

### 1. 颜色分析
```python
import pandas as pd
data = pd.read_csv('result.csv')
# 统计主要颜色
color_counts = data['RGB_Hex'].value_counts()
print(color_counts.head())
```

### 2. 空间分布分析
```python
# 分析红色像素分布
red_pixels = data[data['R'] > 200]
print(f"红色像素位置: {len(red_pixels)} 个")
```

### 3. 图像重建
```python
import numpy as np
from PIL import Image

# 从CSV重建图像
max_x, max_y = data['X'].max(), data['Y'].max()
img_array = np.zeros((max_y+1, max_x+1, 3), dtype=np.uint8)

for _, row in data.iterrows():
    img_array[row['Y'], row['X']] = [row['R'], row['G'], row['B']]
    
Image.fromarray(img_array).save('reconstructed.png')
```

## 扩展功能

未来版本可能包含的功能：
- 支持更多颜色空间（HSV、LAB等）
- 颜色聚类和分割功能
- 批量处理多个文件
- 实时颜色统计显示
- 自定义颜色映射

## 技术支持

如果遇到问题，请检查：
1. Python版本是否为3.7+
2. 所有依赖包是否正确安装
3. 输入文件格式是否支持
4. 系统内存是否充足（RGB处理需要更多内存）

更多技术细节请参考README.md文件。