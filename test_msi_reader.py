#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MSI Reader 测试脚本
用于测试MSI文件分辨率读取功能
"""

import os
import sys
from msi_reader import MSIReader, get_msi_resolution

def test_msi_reader():
    """测试MSI读取器的各种功能"""
    print("=" * 50)
    print("MSI Reader 功能测试")
    print("=" * 50)
    
    # 测试1: 测试不存在的文件
    print("\n1. 测试不存在的文件:")
    try:
        result = get_msi_resolution("nonexistent.imzml")
        print(f"   结果: {result}")
    except Exception as e:
        print(f"   预期错误: {e}")
    
    # 测试2: 测试不支持的文件格式
    print("\n2. 测试不支持的文件格式:")
    try:
        result = get_msi_resolution("test.txt")
        print(f"   结果: {result}")
    except Exception as e:
        print(f"   预期错误: {e}")
    
    # 测试3: 测试分辨率验证功能
    print("\n3. 测试分辨率验证:")
    test_cases = [
        (64, 64),      # 正常
        (0, 64),       # 无效
        (-1, 64),      # 无效
        (64, 0),       # 无效
        (20000, 64),   # 过大
        (64, 20000),   # 过大
    ]
    
    for width, height in test_cases:
        is_valid = MSIReader.validate_resolution((width, height))
        print(f"   {width} x {height}: {'✓ 有效' if is_valid else '✗ 无效'}")
    
    # 测试4: 测试默认分辨率功能
    print("\n4. 测试默认分辨率:")
    try:
        mzml_res = MSIReader.read_mzml_resolution("dummy.mzml")
        print(f"   mzML默认分辨率: {mzml_res}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 测试5: 检查pyimzml库是否可用
    print("\n5. 检查依赖库:")
    try:
        from pyimzml.ImzMLParser import ImzMLParser
        print("   ✓ pyimzml库已安装")
    except ImportError:
        print("   ✗ pyimzml库未安装")
        print("   请运行: pip install pyimzml")
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

def create_sample_imzml():
    """创建一个简单的示例imzML文件用于测试"""
    print("\n创建示例imzML文件...")
    
    # 这里只是创建一个空文件作为占位符
    # 真实的imzML文件需要复杂的XML结构
    sample_path = "sample_test.imzml"
    
    try:
        with open(sample_path, 'w') as f:
            f.write("<?xml version='1.0' encoding='UTF-8'?>\n")
            f.write("<!-- 这是一个测试用的空imzML文件 -->\n")
        
        print(f"   创建了测试文件: {sample_path}")
        print("   注意: 这不是真实的imzML文件，只用于测试文件存在性")
        
        return sample_path
    except Exception as e:
        print(f"   创建文件失败: {e}")
        return None

def cleanup_test_files():
    """清理测试文件"""
    test_files = ["sample_test.imzml"]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"   清理文件: {file_path}")
            except Exception as e:
                print(f"   清理失败: {file_path} - {e}")

def main():
    """主函数"""
    print("MSI Atlas Converter - MSI Reader 测试")
    print(f"Python版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    
    # 运行基本测试
    test_msi_reader()
    
    # 创建和测试示例文件
    sample_file = create_sample_imzml()
    if sample_file:
        print(f"\n测试示例文件: {sample_file}")
        try:
            result = get_msi_resolution(sample_file)
            print(f"   结果: {result}")
        except Exception as e:
            print(f"   错误: {e}")
    
    # 清理
    print("\n清理测试文件:")
    cleanup_test_files()
    
    print("\n测试脚本执行完成！")
    print("\n使用建议:")
    print("1. 确保已安装pyimzml: pip install pyimzml")
    print("2. 使用真实的imzML文件测试完整功能")
    print("3. 检查控制台输出中的错误信息")

if __name__ == "__main__":
    main()