#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RGB匹配调试工具
帮助用户诊断颜色与标签的对应关系问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import MSIAtlasConverter
import tkinter as tk
from PIL import Image
import numpy as np

def debug_rgb_matching(atlas_image_path=None, label_file_path=None):
    """调试RGB匹配功能"""
    print("=== RGB匹配调试工具 ===")
    
    # 创建应用实例
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    app = MSIAtlasConverter(root)
    
    # 使用默认路径或用户指定路径
    if not label_file_path:
        label_file_path = "test data/labels.txt"
    if not atlas_image_path:
        atlas_image_path = "test_data/test_atlas.png"
    
    print(f"\n1. 加载label文件: {label_file_path}")
    if os.path.exists(label_file_path):
        app.load_label_file(label_file_path)
        
        if app.label_data:
            print(f"   ✓ 成功加载 {len(app.label_data)} 个RGB映射")
            
            # 分析label文件中的唯一RGB值
            unique_labels = set(app.label_data.values())
            print(f"   ✓ 包含 {len(unique_labels)} 个唯一标签")
            
            # 提取原始RGB值（从逗号分隔格式）
            original_rgb_values = set()
            for key in app.label_data.keys():
                if ',' in key and not key.startswith('(') and not key.startswith('#'):
                    try:
                        parts = key.split(',')
                        if len(parts) == 3:
                            r, g, b = map(int, parts)
                            original_rgb_values.add((r, g, b))
                    except ValueError:
                        continue
            
            print(f"   ✓ 包含 {len(original_rgb_values)} 个唯一RGB值")
            
            # 显示前10个RGB-标签对应关系
            print("\n2. RGB-标签对应关系（前10个）:")
            count = 0
            for rgb in sorted(original_rgb_values):
                if count < 10:
                    matched_label = app.get_label_for_rgb(rgb[0], rgb[1], rgb[2])
                    print(f"   RGB({rgb[0]:3d},{rgb[1]:3d},{rgb[2]:3d}) -> {matched_label}")
                    count += 1
                else:
                    break
        else:
            print("   ✗ 加载label文件失败")
            return
    else:
        print(f"   ✗ 找不到label文件: {label_file_path}")
        return
    
    # 如果有Atlas图像，分析其RGB值
    print(f"\n3. 分析Atlas图像: {atlas_image_path}")
    if os.path.exists(atlas_image_path):
        try:
            atlas_image = Image.open(atlas_image_path)
            atlas_array = np.array(atlas_image.convert('RGB'))
            
            print(f"   ✓ 图像尺寸: {atlas_array.shape}")
            
            # 获取图像中的唯一RGB值
            reshaped = atlas_array.reshape(-1, 3)
            unique_colors = np.unique(reshaped, axis=0)
            
            print(f"   ✓ 图像包含 {len(unique_colors)} 个唯一颜色")
            
            # 检查每个颜色的标签匹配情况
            print("\n4. 图像颜色标签匹配情况:")
            matched_count = 0
            unmatched_colors = []
            
            for color in unique_colors[:20]:  # 只检查前20个颜色
                r, g, b = color
                label = app.get_label_for_rgb(int(r), int(g), int(b))
                if label:
                    matched_count += 1
                    status = "✓"
                else:
                    status = "✗"
                    unmatched_colors.append((r, g, b))
                
                print(f"   RGB({r:3d},{g:3d},{b:3d}) -> {label or 'Unknown'} {status}")
            
            print(f"\n   匹配统计: {matched_count}/{min(20, len(unique_colors))} 个颜色有标签")
            
            if unmatched_colors:
                print(f"\n5. 未匹配的颜色（前5个）:")
                for i, (r, g, b) in enumerate(unmatched_colors[:5]):
                    print(f"   RGB({r:3d},{g:3d},{b:3d})")
                    
                    # 查找最接近的颜色
                    min_distance = float('inf')
                    closest_rgb = None
                    closest_label = None
                    
                    for orig_rgb in original_rgb_values:
                        distance = sum((a - b) ** 2 for a, b in zip((r, g, b), orig_rgb)) ** 0.5
                        if distance < min_distance:
                            min_distance = distance
                            closest_rgb = orig_rgb
                            closest_label = app.get_label_for_rgb(*orig_rgb)
                    
                    if closest_rgb:
                        print(f"     最接近: RGB{closest_rgb} -> {closest_label} (距离: {min_distance:.1f})")
            
        except Exception as e:
            print(f"   ✗ 加载图像失败: {e}")
    else:
        print(f"   ✗ 找不到Atlas图像: {atlas_image_path}")
    
    root.destroy()
    print("\n=== 调试完成 ===")
    print("\n建议:")
    print("1. 如果大部分颜色未匹配，检查label文件格式是否正确")
    print("2. 如果颜色接近但不完全匹配，可能需要颜色容差匹配")
    print("3. 确保Atlas图像和label文件使用相同的颜色编码")

if __name__ == "__main__":
    # 可以通过命令行参数指定文件路径
    atlas_path = sys.argv[1] if len(sys.argv) > 1 else None
    label_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    debug_rgb_matching(atlas_path, label_path)