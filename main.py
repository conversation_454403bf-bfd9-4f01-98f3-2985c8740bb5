import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import numpy as np
import pandas as pd
from PIL import Image, ImageTk
import cv2
from scipy import stats
import os
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.patches as patches
from msi_reader import MSIReader, get_msi_resolution
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MSIAtlasConverter:
    def __init__(self, root):
        self.root = root
        self.root.title("MSI Atlas Converter v2.1")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # 设置图标
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass  # 如果图标文件不存在，忽略错误
        
        # 变量
        self.file_path_var = tk.StringVar()
        self.msi_file_path = tk.StringVar()
        self.atlas_file_path = tk.StringVar()
        self.label_file_path = tk.StringVar()
        self.width_var = tk.StringVar(value="64")
        self.height_var = tk.StringVar(value="64")
        self.output_dir_var = tk.StringVar()
        self.method_var = tk.StringVar(value="Nearest Neighbor")
        self.output_format_var = tk.StringVar(value="CSV")
        self.downsampling_method = tk.StringVar(value="Nearest Neighbor")
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="就绪")
        self.current_file = None
        self.current_resolution = None
        self.label_data = None
        self.manual_resolution_var = tk.BooleanVar()
        self.manual_width_var = tk.StringVar(value="64")
        self.manual_height_var = tk.StringVar(value="64")
        
        # 创建界面
        self.setup_ui()
        
        # 初始化缓存管理
        self.init_cache_management()
        
        # 创建菜单栏
        self.create_menu()
    
    def init_cache_management(self):
        """初始化缓存管理"""
        try:
            from optimized_msi_reader import get_msi_cache_info
            cache_info = get_msi_cache_info()
            logger.info(f"缓存初始化: 内存缓存 {cache_info['memory_cache_count']} 项, "
                       f"磁盘缓存 {cache_info['disk_cache_count']} 项")
        except Exception as e:
            logger.warning(f"缓存初始化失败: {e}")
    
    def create_progress_window(self, title: str):
        """创建进度显示窗口"""
        progress_window = tk.Toplevel(self.root)
        progress_window.title(title)
        progress_window.geometry("400x150")
        progress_window.resizable(False, False)
        
        # 居中显示
        progress_window.transient(self.root)
        progress_window.grab_set()
        
        # 计算居中位置
        x = (progress_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (progress_window.winfo_screenheight() // 2) - (150 // 2)
        progress_window.geometry(f"400x150+{x}+{y}")
        
        # 创建进度条
        progress_frame = ttk.Frame(progress_window)
        progress_frame.pack(expand=True, fill='both', padx=20, pady=20)
        
        # 标题标签
        title_label = ttk.Label(progress_frame, text=title, font=('Arial', 10))
        title_label.pack(pady=(0, 10))
        
        # 进度条
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(
            progress_frame, 
            variable=progress_var, 
            maximum=100, 
            length=350,
            mode='determinate'
        )
        progress_bar.pack(pady=(0, 10))
        
        # 进度文本
        progress_text = ttk.Label(progress_frame, text="0%")
        progress_text.pack()
        
        # 更新进度文本的函数
        def update_progress_text(*args):
            progress_text.config(text=f"{progress_var.get():.1f}%")
        
        progress_var.trace('w', update_progress_text)
        
        # 保存变量到窗口对象
        progress_window.progress_var = progress_var
        progress_window.progress_bar = progress_bar
        progress_window.progress_text = progress_text
        
        progress_window.update()
        return progress_window
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="缓存管理", command=self.show_cache_management)
        tools_menu.add_command(label="性能测试", command=self.run_performance_test)
        tools_menu.add_separator()
        tools_menu.add_command(label="故障排除", command=self.show_troubleshooting)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用指南", command=self.show_user_guide)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def show_cache_management(self):
        """显示缓存管理窗口"""
        cache_window = tk.Toplevel(self.root)
        cache_window.title("缓存管理")
        cache_window.geometry("500x400")
        cache_window.resizable(False, False)
        
        # 居中显示
        cache_window.transient(self.root)
        cache_window.grab_set()
        
        # 计算居中位置
        x = (cache_window.winfo_screenwidth() // 2) - (500 // 2)
        y = (cache_window.winfo_screenheight() // 2) - (400 // 2)
        cache_window.geometry(f"500x400+{x}+{y}")
        
        # 创建主框架
        main_frame = ttk.Frame(cache_window)
        main_frame.pack(expand=True, fill='both', padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="分辨率读取缓存管理", font=('Arial', 12, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 缓存信息框架
        info_frame = ttk.LabelFrame(main_frame, text="缓存信息", padding=10)
        info_frame.pack(fill='x', pady=(0, 20))
        
        # 获取缓存信息
        try:
            from optimized_msi_reader import get_msi_cache_info
            cache_info = get_msi_cache_info()
            
            info_text = f"内存缓存: {cache_info['memory_cache_count']} 项\n"
            info_text += f"磁盘缓存: {cache_info['disk_cache_count']} 项\n"
            info_text += f"缓存目录: {cache_info['cache_dir']}"
            
        except Exception as e:
            info_text = f"获取缓存信息失败: {str(e)}"
        
        info_label = ttk.Label(info_frame, text=info_text, justify='left')
        info_label.pack(anchor='w')
        
        # 操作按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(0, 20))
        
        # 刷新按钮
        def refresh_cache_info():
            try:
                from optimized_msi_reader import get_msi_cache_info
                cache_info = get_msi_cache_info()
                
                new_info_text = f"内存缓存: {cache_info['memory_cache_count']} 项\n"
                new_info_text += f"磁盘缓存: {cache_info['disk_cache_count']} 项\n"
                new_info_text += f"缓存目录: {cache_info['cache_dir']}"
                
                info_label.config(text=new_info_text)
                messagebox.showinfo("提示", "缓存信息已刷新")
                
            except Exception as e:
                messagebox.showerror("错误", f"刷新缓存信息失败: {str(e)}")
        
        refresh_btn = ttk.Button(button_frame, text="刷新信息", command=refresh_cache_info)
        refresh_btn.pack(side='left', padx=(0, 10))
        
        # 清理缓存按钮
        def clear_cache():
            result = messagebox.askyesno(
                "确认清理", 
                "确定要清理所有缓存吗？\n\n这将删除所有已保存的分辨率信息，\n下次读取时需要重新计算。"
            )
            
            if result:
                try:
                    from optimized_msi_reader import clear_msi_cache
                    clear_msi_cache()
                    messagebox.showinfo("成功", "缓存已清理")
                    refresh_cache_info()  # 刷新显示
                    
                except Exception as e:
                    messagebox.showerror("错误", f"清理缓存失败: {str(e)}")
        
        clear_btn = ttk.Button(button_frame, text="清理缓存", command=clear_cache)
        clear_btn.pack(side='left')
        
        # 说明文本
        help_frame = ttk.LabelFrame(main_frame, text="说明", padding=10)
        help_frame.pack(fill='both', expand=True)
        
        help_text = (
            "缓存机制说明:\n\n"
            "• 内存缓存: 存储在程序运行期间，程序关闭后自动清除\n"
            "• 磁盘缓存: 存储在本地文件中，可跨程序运行保持\n"
            "• 缓存基于文件路径、大小和修改时间生成唯一标识\n"
            "• 文件发生变化时，缓存会自动失效\n\n"
            "建议:\n"
            "• 定期清理缓存以释放磁盘空间\n"
            "• 如果分辨率读取结果异常，可尝试清理缓存后重试"
        )
        
        help_label = ttk.Label(help_frame, text=help_text, justify='left', wraplength=450)
        help_label.pack(anchor='w')
        
        # 关闭按钮
        close_btn = ttk.Button(main_frame, text="关闭", command=cache_window.destroy)
        close_btn.pack(pady=(20, 0))
    
    def run_performance_test(self):
        """运行性能测试"""
        result = messagebox.askyesno(
            "性能测试", 
            "即将运行分辨率读取性能基准测试\n\n"
            "测试将创建临时文件并比较原始版本和优化版本的性能\n"
            "测试可能需要几分钟时间，是否继续？"
        )
        
        if result:
            try:
                # 在新线程中运行测试，避免阻塞UI
                import threading
                
                def run_test():
                    try:
                        import subprocess
                        import sys
                        
                        # 运行基准测试脚本
                        result = subprocess.run(
                            [sys.executable, "benchmark_resolution_reading.py"],
                            capture_output=True,
                            text=True,
                            cwd=os.getcwd()
                        )
                        
                        # 在主线程中显示结果
                        def show_result():
                            if result.returncode == 0:
                                messagebox.showinfo(
                                    "测试完成", 
                                    "性能测试已完成！\n\n"
                                    "详细结果已保存到 benchmark_results.json\n"
                                    "请查看控制台输出获取详细信息"
                                )
                            else:
                                messagebox.showerror(
                                    "测试失败", 
                                    f"性能测试失败:\n\n{result.stderr}"
                                )
                        
                        self.root.after(0, show_result)
                        
                    except Exception as e:
                        def show_error():
                            messagebox.showerror("错误", f"运行性能测试时发生错误: {str(e)}")
                        
                        self.root.after(0, show_error)
                
                test_thread = threading.Thread(target=run_test)
                test_thread.daemon = True
                test_thread.start()
                
                messagebox.showinfo("提示", "性能测试已在后台启动，请稍候...")
                
            except Exception as e:
                messagebox.showerror("错误", f"启动性能测试失败: {str(e)}")
    
    def show_troubleshooting(self):
        """显示故障排除指南"""
        try:
            import webbrowser
            import os
            
            # 尝试打开故障排除指南文件
            guide_file = "故障排除指南.md"
            if os.path.exists(guide_file):
                os.startfile(guide_file)  # Windows
            else:
                messagebox.showinfo(
                    "故障排除指南", 
                    "故障排除指南文件未找到\n\n"
                    "请查看项目目录中的 故障排除指南.md 文件"
                )
        except Exception as e:
            messagebox.showerror("错误", f"打开故障排除指南失败: {str(e)}")
    
    def show_user_guide(self):
        """显示使用指南"""
        try:
            import os
            
            # 尝试打开使用指南文件
            guide_files = ["分辨率读取最佳实践.md", "README.md"]
            
            for guide_file in guide_files:
                if os.path.exists(guide_file):
                    os.startfile(guide_file)  # Windows
                    return
            
            messagebox.showinfo(
                "使用指南", 
                "使用指南文件未找到\n\n"
                "请查看项目目录中的相关文档文件"
            )
        except Exception as e:
            messagebox.showerror("错误", f"打开使用指南失败: {str(e)}")
    
    def show_about(self):
        """显示关于信息"""
        about_text = (
            "MSI Atlas Converter v2.1\n\n"
            "质谱成像数据转换工具\n\n"
            "主要功能:\n"
            "• 优化的分辨率读取算法\n"
            "• 智能缓存机制\n"
            "• 性能监控和测试\n"
            "• 详细的错误处理\n\n"
            "更新内容 (v2.1):\n"
            "• 全新的优化分辨率读取引擎\n"
            "• 智能采样和网格分析\n"
            "• 置信度评估系统\n"
            "• 缓存管理功能\n"
            "• 性能基准测试\n\n"
            "技术支持: 查看故障排除指南"
        )
        
        messagebox.showinfo("关于", about_text)
        
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧工具栏
        left_frame = ttk.Frame(main_frame, width=300)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.pack_propagate(False)
        
        # 右侧视图框架
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.setup_left_panel(left_frame)
        self.setup_right_panel(right_frame)
        
    def setup_left_panel(self, parent):
        # 标题
        title_label = ttk.Label(parent, text="MSI Atlas Converter", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # MSI文件输入
        msi_frame = ttk.LabelFrame(parent, text="质谱成像文件", padding=10)
        msi_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Entry(msi_frame, textvariable=self.msi_file_path, width=35).pack(pady=(0, 5))
        ttk.Button(msi_frame, text="选择MSI文件", command=self.select_msi_file).pack()
        
        self.msi_info_label = ttk.Label(msi_frame, text="分辨率: 未选择文件", foreground="gray")
        self.msi_info_label.pack(pady=(5, 0))
        
        # 手动输入分辨率选项
        manual_res_frame = ttk.Frame(msi_frame)
        manual_res_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.manual_resolution_cb = ttk.Checkbutton(
            manual_res_frame, 
            text="手动输入分辨率", 
            variable=self.manual_resolution_var,
            command=self.toggle_manual_resolution
        )
        self.manual_resolution_cb.pack(anchor=tk.W)
        
        # 手动分辨率输入框
        self.manual_input_frame = ttk.Frame(manual_res_frame)
        self.manual_input_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(self.manual_input_frame, text="宽度:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.manual_width_entry = ttk.Entry(self.manual_input_frame, textvariable=self.manual_width_var, width=10)
        self.manual_width_entry.grid(row=0, column=1, padx=(0, 10))
        
        ttk.Label(self.manual_input_frame, text="高度:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.manual_height_entry = ttk.Entry(self.manual_input_frame, textvariable=self.manual_height_var, width=10)
        self.manual_height_entry.grid(row=0, column=3)
        
        # 绑定输入验证事件
        self.manual_width_var.trace('w', lambda *args: self.update_resolution_display())
        self.manual_height_var.trace('w', lambda *args: self.update_resolution_display())
        
        # 初始状态下禁用手动输入
        self.manual_width_entry.config(state=tk.DISABLED)
        self.manual_height_entry.config(state=tk.DISABLED)
        
        # Atlas图像文件输入
        atlas_frame = ttk.LabelFrame(parent, text="Atlas图像文件", padding=10)
        atlas_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Entry(atlas_frame, textvariable=self.atlas_file_path, width=35).pack(pady=(0, 5))
        ttk.Button(atlas_frame, text="选择Atlas文件", command=self.select_atlas_file).pack()
        
        # Label文件输入
        label_frame = ttk.LabelFrame(parent, text="Label文件 (可选)", padding=10)
        label_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Entry(label_frame, textvariable=self.label_file_path, width=35).pack(pady=(0, 5))
        ttk.Button(label_frame, text="选择Label文件", command=self.select_label_file).pack()
        
        self.label_info_label = ttk.Label(label_frame, text="格式: RGB序号对应root列标签", foreground="gray")
        self.label_info_label.pack(pady=(5, 0))
        
        # 降采样方法选择
        method_frame = ttk.LabelFrame(parent, text="降采样方法", padding=10)
        method_frame.pack(fill=tk.X, pady=(0, 20))
        
        methods = ["Nearest Neighbor", "Mode Downsampling", "Nearest-Neighbor-based Mode Downsampling"]
        for method in methods:
            ttk.Radiobutton(method_frame, text=method, variable=self.downsampling_method, 
                          value=method).pack(anchor=tk.W, pady=2)
        
        # 生成按钮
        ttk.Button(parent, text="生成CSV文件", command=self.generate_csv, 
                  style="Accent.TButton").pack(pady=(0, 10))
        
        # 下载按钮框架
        download_frame = ttk.LabelFrame(parent, text="下载", padding=10)
        download_frame.pack(fill=tk.X)
        
        self.download_csv_btn = ttk.Button(download_frame, text="下载CSV文件", 
                                         command=self.download_csv, state=tk.DISABLED)
        self.download_csv_btn.pack(pady=(0, 5))
        
        self.download_img_btn = ttk.Button(download_frame, text="下载图像文件", 
                                         command=self.download_image, state=tk.DISABLED)
        self.download_img_btn.pack()
        
    def setup_right_panel(self, parent):
        # 右侧分为两个视图框
        # 上方：输入图像显示
        input_frame = ttk.LabelFrame(parent, text="输入图像", padding=5)
        input_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        self.input_canvas = tk.Canvas(input_frame, bg="white")
        self.input_canvas.pack(fill=tk.BOTH, expand=True)
        
        # 下方：生成图像显示
        output_frame = ttk.LabelFrame(parent, text="生成图像", padding=5)
        output_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        self.output_canvas = tk.Canvas(output_frame, bg="white")
        self.output_canvas.pack(fill=tk.BOTH, expand=True)
        
    def select_msi_file(self):
        file_path = filedialog.askopenfilename(
            title="选择质谱成像文件",
            filetypes=[("所有支持格式", "*.imzML *.mzML"), 
                      ("imzML文件", "*.imzML"), 
                      ("mzML文件", "*.mzML")]
        )
        if file_path:
            self.msi_file_path.set(file_path)
            if not self.manual_resolution_var.get():
                self.read_msi_resolution(file_path)
            else:
                self.update_resolution_display()
                
    def toggle_manual_resolution(self):
        """切换手动输入分辨率模式"""
        if self.manual_resolution_var.get():
            # 启用手动输入
            self.manual_width_entry.config(state=tk.NORMAL)
            self.manual_height_entry.config(state=tk.NORMAL)
            self.update_resolution_display()
        else:
            # 禁用手动输入
            self.manual_width_entry.config(state=tk.DISABLED)
            self.manual_height_entry.config(state=tk.DISABLED)
            # 如果已选择文件，重新读取分辨率
            if self.msi_file_path.get():
                self.read_msi_resolution(self.msi_file_path.get())
            else:
                self.msi_info_label.config(text="分辨率: 未选择文件", foreground="gray")
                
    def update_resolution_display(self):
        """更新分辨率显示"""
        if self.manual_resolution_var.get():
            try:
                width_str = self.manual_width_var.get().strip()
                height_str = self.manual_height_var.get().strip()
                
                if not width_str or not height_str:
                    self.msi_info_label.config(
                        text="分辨率: 请输入完整的宽度和高度", 
                        foreground="orange"
                    )
                    self.msi_resolution = None
                    return
                    
                width = int(width_str)
                height = int(height_str)
                
                if width <= 0 or height <= 0:
                    self.msi_info_label.config(
                        text="分辨率: 宽度和高度必须大于0", 
                        foreground="red"
                    )
                    self.msi_resolution = None
                    return
                    
                self.msi_resolution = (width, height)
                self.msi_info_label.config(
                    text=f"分辨率: {width} x {height} (手动输入)", 
                    foreground="blue"
                )
            except ValueError:
                self.msi_info_label.config(
                    text="分辨率: 请输入有效的数字", 
                    foreground="red"
                )
                self.msi_resolution = None
            
    def select_atlas_file(self):
        file_path = filedialog.askopenfilename(
            title="选择Atlas图像文件",
            filetypes=[("图像文件", "*.png *.jpg *.jpeg"), 
                      ("PNG文件", "*.png"), 
                      ("JPEG文件", "*.jpg *.jpeg")]
        )
        if file_path:
            self.atlas_file_path.set(file_path)
            self.load_atlas_image(file_path)
            
    def select_label_file(self):
        file_path = filedialog.askopenfilename(
            title="选择Label文件",
            filetypes=[("文本文件", "*.txt"), 
                      ("所有文件", "*.*")]
        )
        if file_path:
            self.label_file_path.set(file_path)
            self.load_label_file(file_path)
            
    def load_label_file(self, file_path):
        """加载label文件并解析RGB到root的映射关系"""
        try:
            # 读取label文件
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 检查文件是否有足够的行数
            if len(lines) < 15:
                messagebox.showerror("错误", "Label文件格式不正确，数据行不足")
                return
            
            # 解析数据行，从第15行开始（索引14）
            # 格式：IDX R G B A VIS MSH LABEL
            # 第2列为R，第3列为G，第4列为B，第8列为label
            self.label_data = {}
            data_count = 0
            
            for i, line in enumerate(lines[14:], start=15):  # 从第15行开始
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                    
                # 分割数据，支持制表符和空格分隔
                parts = line.split()
                if len(parts) >= 8:
                    try:
                        # 提取RGB值（第2、3、4列，索引1、2、3）
                        r = int(parts[1])
                        g = int(parts[2]) 
                        b = int(parts[3])
                        
                        # 提取label（第8列，索引7）
                        label = parts[7].strip('"')  # 移除引号
                        
                        # 创建RGB键的多种格式，与get_label_for_rgb方法保持一致
                        rgb_formats = [
                            f'{r},{g},{b}',  # 逗号分隔
                            f'{r} {g} {b}',  # 空格分隔
                            f'{r}-{g}-{b}',  # 横线分隔
                            f'({r},{g},{b})',  # 括号包围
                            f'({r}, {g}, {b})',  # 括号包围带空格
                            f'[{r},{g},{b}]',  # 方括号包围
                            f'{r:03d}{g:03d}{b:03d}',  # 三位数字连接
                            f'{r}{g}{b}',  # 直接连接
                            f'#{r:02x}{g:02x}{b:02x}',  # 十六进制格式
                            f'#{r:02X}{g:02X}{b:02X}',  # 大写十六进制
                            f'rgb({r},{g},{b})'  # CSS格式
                        ]
                        
                        # 存储所有格式的映射
                        for rgb_format in rgb_formats:
                            self.label_data[rgb_format] = label
                        
                        data_count += 1
                        
                    except (ValueError, IndexError) as e:
                        logger.warning(f"跳过第{i}行，解析错误: {line[:50]}...")
                        continue
            
            # 更新状态显示
            unique_labels = len(set(self.label_data.values()))
            self.label_info_label.config(
                text=f"已加载 {data_count} 个标签，{unique_labels} 个唯一类别", 
                foreground="green"
            )
            
            logger.info(f"成功加载ITK-SnAP label文件: {data_count} 个标签，{unique_labels} 个唯一类别")
            
            if data_count == 0:
                messagebox.showwarning("警告", "未找到有效的标签数据，请检查文件格式")
                self.label_data = None
            
        except Exception as e:
            messagebox.showerror("错误", f"加载label文件失败: {str(e)}")
            logger.error(f"加载label文件失败: {e}")
            self.label_data = None
            self.label_info_label.config(
                text="格式: RGB序号对应root列标签", 
                foreground="gray"
            )
            
    def read_msi_resolution(self, file_path):
        """读取MSI文件分辨率（优化版本）"""
        try:
            # 尝试使用优化读取器
            try:
                from optimized_msi_reader import get_optimized_msi_resolution
                
                # 显示加载状态
                self.msi_info_label.config(text="分辨率: 正在使用优化算法读取...", foreground="blue")
                self.root.update()
                
                # 读取分辨率信息
                resolution_info = get_optimized_msi_resolution(file_path)
                
                if resolution_info:
                    width, height = resolution_info.width, resolution_info.height
                    self.msi_resolution = (width, height)
                    
                    # 根据文件类型和置信度更新状态
                    file_extension = os.path.splitext(file_path)[1].lower()
                    confidence_text = f"置信度: {resolution_info.confidence:.1%}"
                    
                    if file_extension == '.imzml':
                        if resolution_info.confidence > 0.8:
                            self.msi_info_label.config(
                                text=f"分辨率: {width} x {height} ({confidence_text})", 
                                foreground="green"
                            )
                        elif resolution_info.confidence > 0.5:
                            self.msi_info_label.config(
                                text=f"分辨率: {width} x {height} ({confidence_text}, 建议验证)", 
                                foreground="orange"
                            )
                        else:
                            self.msi_info_label.config(
                                text=f"分辨率: {width} x {height} ({confidence_text}, 低置信度)", 
                                foreground="red"
                            )
                        
                        # 显示详细信息
                        details = f"分辨率: {width}x{height}\n"
                        details += f"总点数: {resolution_info.total_points:,}\n"
                        details += f"网格密度: {resolution_info.grid_density:.1%}\n"
                        details += f"规则网格: {'是' if resolution_info.is_regular_grid else '否'}\n"
                        details += f"处理时间: {resolution_info.processing_time:.2f}秒\n"
                        details += f"文件大小: {resolution_info.file_size / 1024 / 1024:.1f}MB\n"
                        details += f"置信度: {resolution_info.confidence:.1%}"
                        
                        messagebox.showinfo("分辨率读取成功", details)
                        print(f"✓ 优化分辨率读取成功: {width} x {height}, 置信度: {resolution_info.confidence:.2f}")
                    else:
                        self.msi_info_label.config(
                            text=f"分辨率: {width} x {height} (默认值，{file_extension}格式)", 
                            foreground="orange"
                        )
                        messagebox.showinfo(
                            "提示", 
                            f"当前版本暂不支持自动读取{file_extension}文件的分辨率\n"
                            f"使用默认分辨率 {width}x{height}\n\n"
                            "如需支持真实分辨率读取，请联系开发者添加相应的解析库"
                        )
                    return
                    
            except ImportError:
                # 回退到原始方法
                pass
            
            # 使用专门的MSI读取模块
            self.msi_info_label.config(text="分辨率: 正在使用基础方法读取...", foreground="blue")
            self.root.update()
            
            resolution = get_msi_resolution(file_path)
            
            if resolution:
                width, height = resolution
                self.msi_resolution = resolution
                
                # 根据文件类型显示不同的状态信息
                file_extension = os.path.splitext(file_path)[1].lower()
                
                if file_extension == '.imzml':
                    self.msi_info_label.config(
                        text=f"分辨率: {width} x {height} (从imzML读取)", 
                        foreground="green"
                    )
                    print(f"✓ 成功读取imzML文件分辨率: {width} x {height}")
                    
                elif file_extension == '.mzml':
                    self.msi_info_label.config(
                        text=f"分辨率: {width} x {height} (默认值，{file_extension}格式)", 
                        foreground="orange"
                    )
                    messagebox.showinfo(
                        "提示", 
                        f"当前版本暂不支持自动读取{file_extension}文件的分辨率\n"
                        f"使用默认分辨率 {width}x{height}\n\n"
                        "如需支持真实分辨率读取，请联系开发者添加相应的解析库"
                    )
                    
            else:
                # 读取失败，显示错误信息
                self.msi_resolution = None
                self.msi_info_label.config(text="分辨率: 读取失败", foreground="red")
                messagebox.showerror("错误", "无法读取MSI文件分辨率，请检查文件格式和完整性")
                
        except ImportError as e:
            self.msi_resolution = None
            self.msi_info_label.config(text="分辨率: 缺少依赖库", foreground="red")
            messagebox.showerror(
                "依赖库错误", 
                f"缺少必要的库:\n{str(e)}\n\n解决方案:\n"
                f"1. 安装pyimzml: pip install pyimzml\n"
                f"2. 或使用conda: conda install -c bioconda pyimzml\n"
                f"3. 检查网络连接后重试"
            )
            
        except FileNotFoundError as e:
            self.msi_resolution = None
            self.msi_info_label.config(text="分辨率: 文件不存在", foreground="red")
            error_msg = str(e)
            if ".ibd" in error_msg:
                messagebox.showerror(
                    "文件错误", 
                    f"imzML文件需要配套的.ibd文件\n\n"
                    f"请确保以下文件都存在:\n"
                    f"• {os.path.splitext(file_path)[0]}.imzML\n"
                    f"• {os.path.splitext(file_path)[0]}.ibd\n\n"
                    f"原始错误: {error_msg}"
                )
            else:
                messagebox.showerror("文件错误", f"找不到文件: {file_path}\n\n请检查文件路径是否正确")
            
        except ValueError as e:
            self.msi_resolution = None
            self.msi_info_label.config(text="分辨率: 文件格式错误", foreground="red")
            error_msg = str(e)
            if "not well-formed" in error_msg or "invalid token" in error_msg:
                messagebox.showerror(
                    "文件格式错误", 
                    f"imzML文件格式错误或损坏\n\n"
                    f"可能的解决方案:\n"
                    f"1. 重新下载文件\n"
                    f"2. 检查文件是否完整\n"
                    f"3. 尝试用其他工具验证文件\n"
                    f"4. 重新生成imzML文件\n\n"
                    f"技术详情: {error_msg}"
                )
            else:
                messagebox.showerror("文件格式错误", f"文件格式错误:\n{error_msg}")
                
        except Exception as e:
            self.msi_resolution = None
            self.msi_info_label.config(text="分辨率: 未知错误", foreground="red")
            messagebox.showerror(
                "未知错误", 
                f"读取MSI文件时发生未知错误:\n{str(e)}\n\n"
                f"建议操作:\n"
                f"1. 检查文件格式和完整性\n"
                f"2. 查看故障排除指南.md\n"
                f"3. 尝试使用较小的测试文件\n"
                f"4. 联系技术支持"
            )
            
    def load_atlas_image(self, file_path):
        try:
            self.atlas_image = Image.open(file_path)
            self.display_input_image()
        except Exception as e:
            messagebox.showerror("错误", f"无法加载Atlas图像: {str(e)}")
            
    def display_input_image(self):
        if self.atlas_image:
            # 调整图像大小以适应画布
            canvas_width = self.input_canvas.winfo_width()
            canvas_height = self.input_canvas.winfo_height()
            
            if canvas_width > 1 and canvas_height > 1:
                img_copy = self.atlas_image.copy()
                img_copy.thumbnail((canvas_width-10, canvas_height-10), Image.Resampling.LANCZOS)
                
                self.input_photo = ImageTk.PhotoImage(img_copy)
                self.input_canvas.delete("all")
                
                x = (canvas_width - img_copy.width) // 2
                y = (canvas_height - img_copy.height) // 2
                self.input_canvas.create_image(x, y, anchor=tk.NW, image=self.input_photo)
                
    def generate_csv(self):
        if not self.msi_file_path.get():
            messagebox.showwarning("警告", "请选择MSI文件")
            return
            
        if not self.atlas_file_path.get():
            messagebox.showwarning("警告", "请选择Atlas图像文件")
            return
            
        if not self.msi_resolution:
            messagebox.showwarning("警告", "无法获取MSI分辨率")
            return
            
        try:
            # 执行降采样和CSV生成
            self.perform_downsampling()
            self.display_output_image()
            
            # 启用下载按钮
            self.download_csv_btn.config(state=tk.NORMAL)
            self.download_img_btn.config(state=tk.NORMAL)
            
            messagebox.showinfo("成功", "CSV文件生成完成！")
            
        except Exception as e:
            messagebox.showerror("错误", f"生成CSV文件时出错: {str(e)}")
            
    def perform_downsampling(self):
        # 将Atlas图像转换为numpy数组，保留RGB信息
        atlas_array = np.array(self.atlas_image)
        
        # 确保是RGB格式
        if len(atlas_array.shape) == 2:
            # 如果是灰度图，转换为RGB
            atlas_array = np.stack([atlas_array] * 3, axis=-1)
        elif atlas_array.shape[2] == 4:
            # 如果是RGBA，转换为RGB
            atlas_array = atlas_array[:, :, :3]
            
        target_height, target_width = self.msi_resolution
        
        method = self.downsampling_method.get()
        
        if method == "Nearest Neighbor":
            downsampled = self.nearest_neighbor_downsampling_rgb(atlas_array, target_width, target_height)
        elif method == "Mode Downsampling":
            downsampled = self.mode_downsampling_rgb(atlas_array, target_width, target_height)
        else:  # Nearest-Neighbor-based Mode Downsampling
            downsampled = self.nn_mode_downsampling_rgb(atlas_array, target_width, target_height)
            
        # 生成RGB CSV数据
        self.generate_rgb_csv(downsampled)
        
        # 生成对应的图像
        self.generated_image = Image.fromarray(downsampled.astype(np.uint8))
        
    def nearest_neighbor_downsampling_rgb(self, image, target_width, target_height):
        return cv2.resize(image, (target_width, target_height), interpolation=cv2.INTER_NEAREST)
        
    def mode_downsampling_rgb(self, image, target_width, target_height):
        original_height, original_width, channels = image.shape
        
        # 计算每个目标像素对应的原始图像区域大小
        block_height = original_height / target_height
        block_width = original_width / target_width
        
        result = np.zeros((target_height, target_width, channels), dtype=image.dtype)
        
        for i in range(target_height):
            for j in range(target_width):
                # 计算对应的原始图像区域
                start_y = int(i * block_height)
                end_y = int((i + 1) * block_height)
                start_x = int(j * block_width)
                end_x = int((j + 1) * block_width)
                
                # 对每个颜色通道分别计算众数
                for c in range(channels):
                    block = image[start_y:end_y, start_x:end_x, c]
                    if block.size > 0:
                        mode_value = stats.mode(block.flatten(), keepdims=True)[0][0]
                        result[i, j, c] = mode_value
                
        return result
        
    def nn_mode_downsampling_rgb(self, image, target_width, target_height):
        # 先进行最近邻降采样
        nn_result = self.nearest_neighbor_downsampling_rgb(image, target_width, target_height)
        
        # 然后在小邻域内应用众数滤波
        result = np.copy(nn_result)
        channels = nn_result.shape[2]
        
        for i in range(1, target_height-1):
            for j in range(1, target_width-1):
                for c in range(channels):
                    neighborhood = nn_result[i-1:i+2, j-1:j+2, c]
                    if neighborhood.size > 0:
                        mode_value = stats.mode(neighborhood.flatten(), keepdims=True)[0][0]
                        result[i, j, c] = mode_value
                
        return result
        
    def generate_rgb_csv(self, rgb_array):
        """生成包含RGB颜色信息的CSV文件"""
        height, width, channels = rgb_array.shape
        
        # 创建包含坐标和RGB值的数据列表
        csv_data = []
        
        for i in range(height):
            for j in range(width):
                r, g, b = rgb_array[i, j]
                rgb_hex = f'#{int(r):02x}{int(g):02x}{int(b):02x}'
                
                # 基础数据
                row_data = {
                    'X': j,
                    'Y': i,
                    'R': int(r),
                    'G': int(g),
                    'B': int(b),
                    'RGB_Hex': rgb_hex
                }
                
                # 如果有label文件，添加注释信息
                if self.label_data:
                    # 尝试多种RGB格式匹配
                    label = self.get_label_for_rgb(int(r), int(g), int(b))
                    # 确保标签完整显示，移除多余的引号和空格
                    if label:
                        label = label.strip('"').strip()
                    row_data['Label'] = label if label else 'Unknown'
                
                csv_data.append(row_data)
        
        self.generated_csv = pd.DataFrame(csv_data)
        
        # 如果有label数据，显示统计信息
        if self.label_data:
            labeled_count = len(self.generated_csv[self.generated_csv['Label'] != 'Unknown'])
            total_count = len(self.generated_csv)
            logger.info(f"Label匹配统计: {labeled_count}/{total_count} 个像素点已标注")
            
    def get_label_for_rgb(self, r, g, b):
        """根据RGB值获取对应的label标签"""
        if not self.label_data:
            return None
            
        # 尝试多种RGB格式进行匹配
        rgb_formats = [
            f'{r},{g},{b}',  # 逗号分隔
            f'{r} {g} {b}',  # 空格分隔
            f'{r}-{g}-{b}',  # 横线分隔
            f'({r},{g},{b})',  # 括号包围
            f'({r}, {g}, {b})',  # 括号包围带空格
            f'[{r},{g},{b}]',  # 方括号包围
            f'{r:03d}{g:03d}{b:03d}',  # 三位数字连接
            f'{r}{g}{b}',  # 直接连接
            f'#{r:02x}{g:02x}{b:02x}',  # 十六进制格式
            f'#{r:02X}{g:02X}{b:02X}',  # 大写十六进制
            f'rgb({r},{g},{b})'  # CSS格式
        ]
        
        # 逐一尝试匹配
        for rgb_format in rgb_formats:
            if rgb_format in self.label_data:
                return self.label_data[rgb_format]
        
        # 如果没有匹配到，记录调试信息（仅记录前几个未匹配的RGB值）
        if not hasattr(self, '_debug_count'):
            self._debug_count = 0
        if self._debug_count < 5:  # 只记录前5个未匹配的RGB值
            logger.debug(f"未找到RGB({r},{g},{b})的标签匹配")
            self._debug_count += 1
                
        return None
        
    def display_output_image(self):
        if self.generated_image:
            # 放大图像以便更好地显示
            display_image = self.generated_image.resize((300, 300), Image.Resampling.NEAREST)
            
            canvas_width = self.output_canvas.winfo_width()
            canvas_height = self.output_canvas.winfo_height()
            
            if canvas_width > 1 and canvas_height > 1:
                self.output_photo = ImageTk.PhotoImage(display_image)
                self.output_canvas.delete("all")
                
                x = (canvas_width - display_image.width) // 2
                y = (canvas_height - display_image.height) // 2
                self.output_canvas.create_image(x, y, anchor=tk.NW, image=self.output_photo)
                
    def download_csv(self):
        if self.generated_csv is not None:
            file_path = filedialog.asksaveasfilename(
                title="保存CSV文件",
                defaultextension=".csv",
                filetypes=[("CSV文件", "*.csv")]
            )
            if file_path:
                self.generated_csv.to_csv(file_path, index=False, header=True)
                
                # 构建保存成功消息
                message = f"RGB CSV文件已保存\n包含 {len(self.generated_csv)} 个像素点的颜色信息"
                
                # 如果有label数据，添加标注统计信息
                if self.label_data and 'Label' in self.generated_csv.columns:
                    labeled_count = len(self.generated_csv[self.generated_csv['Label'] != 'Unknown'])
                    total_count = len(self.generated_csv)
                    label_stats = self.generated_csv['Label'].value_counts()
                    
                    message += f"\n\n标注统计:\n已标注: {labeled_count}/{total_count} 个像素点"
                    message += f"\n未标注: {total_count - labeled_count} 个像素点"
                    
                    # 显示前5个最常见的标签
                    if len(label_stats) > 1:  # 排除'Unknown'
                        top_labels = label_stats[label_stats.index != 'Unknown'].head(5)
                        if len(top_labels) > 0:
                            message += "\n\n主要标签:"
                            for label, count in top_labels.items():
                                message += f"\n• {label}: {count} 个像素点"
                
                messagebox.showinfo("成功", message)
                
    def download_image(self):
        if self.generated_image is not None:
            file_path = filedialog.asksaveasfilename(
                title="保存图像文件",
                defaultextension=".png",
                filetypes=[("PNG文件", "*.png"), ("JPEG文件", "*.jpg")]
            )
            if file_path:
                # 放大图像以便保存
                save_image = self.generated_image.resize((300, 300), Image.Resampling.NEAREST)
                save_image.save(file_path)
                messagebox.showinfo("成功", "图像文件已保存")

def main():
    root = tk.Tk()
    app = MSIAtlasConverter(root)
    
    # 绑定窗口大小变化事件
    def on_configure(event):
        if hasattr(app, 'atlas_image') and app.atlas_image:
            app.display_input_image()
        if hasattr(app, 'generated_image') and app.generated_image:
            app.display_output_image()
            
    root.bind('<Configure>', on_configure)
    root.mainloop()

if __name__ == "__main__":
    main()