#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分辨率读取性能基准测试
比较原始版本和优化版本的性能差异
"""

import os
import time
import sys
import logging
import tempfile
from typing import Dict, List, Tuple, Optional
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PerformanceBenchmark:
    """性能基准测试类"""
    
    def __init__(self):
        self.results = []
        
    def create_test_imzml(self, width: int, height: int, output_path: str) -> bool:
        """
        创建测试用的imzML文件
        
        Args:
            width: 图像宽度
            height: 图像高度
            output_path: 输出文件路径
            
        Returns:
            bool: 是否创建成功
        """
        try:
            # 创建简单的imzML文件内容
            imzml_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<mzML xmlns="http://psi.hupo.org/ms/mzml" version="1.1.0">
  <cvList count="2">
    <cv id="MS" fullName="Proteomics Standards Initiative Mass Spectrometry Ontology" version="4.1.0" URI="https://raw.githubusercontent.com/HUPO-PSI/psi-ms-CV/master/psi-ms.obo"/>
    <cv id="IMS" fullName="Imaging MS Ontology" version="1.1.0" URI="https://raw.githubusercontent.com/HUPO-PSI/psi-ms-CV/master/psi-ms.obo"/>
  </cvList>
  <fileDescription>
    <fileContent>
      <cvParam cvRef="MS" accession="MS:1000579" name="MS1 spectrum"/>
      <cvParam cvRef="IMS" accession="IMS:1000080" name="universally unique identifier"/>
    </fileContent>
  </fileDescription>
  <run id="test_run">
    <spectrumList count="{width * height}">
'''
            
            # 添加光谱数据
            spectrum_id = 1
            for y in range(1, height + 1):
                for x in range(1, width + 1):
                    imzml_content += f'''      <spectrum id="spectrum_{spectrum_id}" index="{spectrum_id - 1}">
        <cvParam cvRef="MS" accession="MS:1000127" name="centroid spectrum"/>
        <cvParam cvRef="IMS" accession="IMS:1000050" name="position x" value="{x}"/>
        <cvParam cvRef="IMS" accession="IMS:1000051" name="position y" value="{y}"/>
        <binaryDataArrayList count="2">
          <binaryDataArray encodedLength="0">
            <cvParam cvRef="MS" accession="MS:1000514" name="m/z array"/>
            <cvParam cvRef="MS" accession="MS:1000523" name="64-bit float"/>
            <cvParam cvRef="MS" accession="MS:1000576" name="no compression"/>
            <binary></binary>
          </binaryDataArray>
          <binaryDataArray encodedLength="0">
            <cvParam cvRef="MS" accession="MS:1000515" name="intensity array"/>
            <cvParam cvRef="MS" accession="MS:1000523" name="64-bit float"/>
            <cvParam cvRef="MS" accession="MS:1000576" name="no compression"/>
            <binary></binary>
          </binaryDataArray>
        </binaryDataArrayList>
      </spectrum>
'''
                    spectrum_id += 1
            
            imzml_content += '''    </spectrumList>
  </run>
</mzML>'''
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(imzml_content)
            
            # 创建对应的.ibd文件（空文件）
            ibd_path = output_path.replace('.imzML', '.ibd')
            with open(ibd_path, 'wb') as f:
                f.write(b'')  # 空的二进制文件
            
            logger.info(f"创建测试文件: {output_path} ({width}x{height})")
            return True
            
        except Exception as e:
            logger.error(f"创建测试文件失败: {e}")
            return False
    
    def benchmark_original_reader(self, file_path: str) -> Dict:
        """
        测试原始读取器性能
        
        Args:
            file_path: 测试文件路径
            
        Returns:
            Dict: 性能结果
        """
        try:
            from msi_reader import get_msi_resolution
            
            start_time = time.time()
            start_memory = self._get_memory_usage()
            
            resolution = get_msi_resolution(file_path)
            
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            return {
                'method': 'Original',
                'success': resolution is not None,
                'resolution': resolution,
                'time': end_time - start_time,
                'memory_delta': end_memory - start_memory,
                'error': None
            }
            
        except Exception as e:
            return {
                'method': 'Original',
                'success': False,
                'resolution': None,
                'time': 0,
                'memory_delta': 0,
                'error': str(e)
            }
    
    def benchmark_optimized_reader(self, file_path: str) -> Dict:
        """
        测试优化读取器性能
        
        Args:
            file_path: 测试文件路径
            
        Returns:
            Dict: 性能结果
        """
        try:
            from optimized_msi_reader import get_optimized_msi_resolution
            
            start_time = time.time()
            start_memory = self._get_memory_usage()
            
            # 进度回调
            progress_values = []
            def progress_callback(progress):
                progress_values.append(progress)
            
            resolution_info = get_optimized_msi_resolution(file_path, progress_callback)
            
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            return {
                'method': 'Optimized',
                'success': resolution_info is not None,
                'resolution': (resolution_info.width, resolution_info.height) if resolution_info else None,
                'resolution_info': resolution_info,
                'time': end_time - start_time,
                'memory_delta': end_memory - start_memory,
                'progress_updates': len(progress_values),
                'error': None
            }
            
        except Exception as e:
            return {
                'method': 'Optimized',
                'success': False,
                'resolution': None,
                'resolution_info': None,
                'time': 0,
                'memory_delta': 0,
                'progress_updates': 0,
                'error': str(e)
            }
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        try:
            import psutil
            process = psutil.Process(os.getpid())
            return process.memory_info().rss / 1024 / 1024  # MB
        except ImportError:
            return 0.0
    
    def run_benchmark_suite(self, test_sizes: List[Tuple[int, int]] = None) -> List[Dict]:
        """
        运行完整的基准测试套件
        
        Args:
            test_sizes: 测试尺寸列表 [(width, height), ...]
            
        Returns:
            List[Dict]: 所有测试结果
        """
        if test_sizes is None:
            test_sizes = [
                (10, 10),      # 小文件
                (50, 50),      # 中等文件
                (100, 100),    # 大文件
                (200, 150),    # 不规则尺寸
            ]
        
        results = []
        temp_dir = tempfile.mkdtemp()
        
        try:
            logger.info("开始性能基准测试")
            logger.info(f"测试尺寸: {test_sizes}")
            logger.info(f"临时目录: {temp_dir}")
            
            for i, (width, height) in enumerate(test_sizes):
                logger.info(f"\n=== 测试 {i+1}/{len(test_sizes)}: {width}x{height} ===")
                
                # 创建测试文件
                test_file = os.path.join(temp_dir, f"test_{width}x{height}.imzML")
                if not self.create_test_imzml(width, height, test_file):
                    logger.error(f"跳过测试 {width}x{height}：文件创建失败")
                    continue
                
                file_size = os.path.getsize(test_file)
                logger.info(f"文件大小: {file_size / 1024:.1f} KB")
                
                # 测试原始读取器
                logger.info("测试原始读取器...")
                original_result = self.benchmark_original_reader(test_file)
                original_result.update({
                    'test_size': (width, height),
                    'file_size': file_size
                })
                results.append(original_result)
                
                # 测试优化读取器
                logger.info("测试优化读取器...")
                optimized_result = self.benchmark_optimized_reader(test_file)
                optimized_result.update({
                    'test_size': (width, height),
                    'file_size': file_size
                })
                results.append(optimized_result)
                
                # 输出对比结果
                self._print_comparison(original_result, optimized_result)
                
                # 清理测试文件
                try:
                    os.remove(test_file)
                    ibd_file = test_file.replace('.imzML', '.ibd')
                    if os.path.exists(ibd_file):
                        os.remove(ibd_file)
                except Exception as e:
                    logger.warning(f"清理文件失败: {e}")
            
            # 输出总结
            self._print_summary(results)
            
        finally:
            # 清理临时目录
            try:
                import shutil
                shutil.rmtree(temp_dir)
            except Exception as e:
                logger.warning(f"清理临时目录失败: {e}")
        
        return results
    
    def _print_comparison(self, original: Dict, optimized: Dict):
        """打印单次测试的对比结果"""
        print(f"\n--- 对比结果 ---")
        print(f"原始读取器:")
        print(f"  成功: {original['success']}")
        print(f"  分辨率: {original['resolution']}")
        print(f"  耗时: {original['time']:.3f}s")
        print(f"  内存变化: {original['memory_delta']:.1f}MB")
        if original['error']:
            print(f"  错误: {original['error']}")
        
        print(f"\n优化读取器:")
        print(f"  成功: {optimized['success']}")
        print(f"  分辨率: {optimized['resolution']}")
        print(f"  耗时: {optimized['time']:.3f}s")
        print(f"  内存变化: {optimized['memory_delta']:.1f}MB")
        print(f"  进度更新次数: {optimized.get('progress_updates', 0)}")
        if optimized['error']:
            print(f"  错误: {optimized['error']}")
        
        # 计算性能提升
        if original['success'] and optimized['success'] and original['time'] > 0:
            time_improvement = (original['time'] - optimized['time']) / original['time'] * 100
            print(f"\n性能提升:")
            print(f"  时间节省: {time_improvement:.1f}%")
            
            if optimized.get('resolution_info'):
                info = optimized['resolution_info']
                print(f"  置信度: {info.confidence:.2f}")
                print(f"  网格密度: {info.grid_density:.2f}")
                print(f"  规则网格: {info.is_regular_grid}")
    
    def _print_summary(self, results: List[Dict]):
        """打印测试总结"""
        print(f"\n{'='*50}")
        print(f"基准测试总结")
        print(f"{'='*50}")
        
        original_results = [r for r in results if r['method'] == 'Original']
        optimized_results = [r for r in results if r['method'] == 'Optimized']
        
        print(f"\n原始读取器:")
        print(f"  成功率: {sum(1 for r in original_results if r['success'])}/{len(original_results)}")
        if original_results:
            avg_time = sum(r['time'] for r in original_results if r['success']) / max(1, sum(1 for r in original_results if r['success']))
            print(f"  平均耗时: {avg_time:.3f}s")
        
        print(f"\n优化读取器:")
        print(f"  成功率: {sum(1 for r in optimized_results if r['success'])}/{len(optimized_results)}")
        if optimized_results:
            avg_time = sum(r['time'] for r in optimized_results if r['success']) / max(1, sum(1 for r in optimized_results if r['success']))
            avg_confidence = sum(r['resolution_info'].confidence for r in optimized_results if r['success'] and r['resolution_info']) / max(1, sum(1 for r in optimized_results if r['success'] and r['resolution_info']))
            print(f"  平均耗时: {avg_time:.3f}s")
            print(f"  平均置信度: {avg_confidence:.2f}")
        
        # 计算总体性能提升
        successful_pairs = []
        for i in range(0, len(results), 2):
            if i + 1 < len(results):
                original = results[i]
                optimized = results[i + 1]
                if original['success'] and optimized['success'] and original['time'] > 0:
                    improvement = (original['time'] - optimized['time']) / original['time'] * 100
                    successful_pairs.append(improvement)
        
        if successful_pairs:
            avg_improvement = sum(successful_pairs) / len(successful_pairs)
            print(f"\n总体性能提升: {avg_improvement:.1f}%")
        
        print(f"\n建议:")
        print(f"  ✓ 使用优化读取器处理大文件")
        print(f"  ✓ 启用缓存机制避免重复读取")
        print(f"  ✓ 关注置信度评估结果")
        print(f"  ✓ 利用进度回调改善用户体验")

def main():
    """主函数"""
    print("MSI分辨率读取性能基准测试")
    print("比较原始版本和优化版本的性能差异")
    
    # 检查依赖
    try:
        import psutil
        print(f"✓ psutil已安装，将监控内存使用")
    except ImportError:
        print(f"⚠ psutil未安装，无法监控内存使用")
        print(f"  安装命令: pip install psutil")
    
    # 运行基准测试
    benchmark = PerformanceBenchmark()
    
    # 自定义测试尺寸
    test_sizes = [
        (20, 20),      # 小文件 - 400点
        (64, 64),      # 中等文件 - 4096点
        (128, 96),     # 大文件 - 12288点
        (200, 150),    # 超大文件 - 30000点
    ]
    
    results = benchmark.run_benchmark_suite(test_sizes)
    
    print(f"\n基准测试完成！")
    print(f"共执行 {len(results)} 个测试")
    
    # 保存结果到文件
    try:
        import json
        results_file = "benchmark_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            # 转换ResolutionInfo对象为字典
            serializable_results = []
            for result in results:
                serializable_result = result.copy()
                if 'resolution_info' in result and result['resolution_info']:
                    serializable_result['resolution_info'] = result['resolution_info'].__dict__
                serializable_results.append(serializable_result)
            
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)
        print(f"结果已保存到: {results_file}")
    except Exception as e:
        print(f"保存结果失败: {e}")

if __name__ == "__main__":
    main()