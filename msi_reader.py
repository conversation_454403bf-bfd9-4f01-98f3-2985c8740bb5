#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MSI文件读取模块
提供对不同格式MSI文件的分辨率读取功能
"""

import os
import logging
from typing import Tuple, Optional

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MSIReader:
    """MSI文件读取器"""
    
    @staticmethod
    def read_imzml_resolution(file_path: str) -> Tuple[int, int]:
        """
        读取imzML文件的分辨率
        
        Args:
            file_path: imzML文件路径
            
        Returns:
            (width, height): 图像分辨率
            
        Raises:
            ImportError: 缺少pyimzml库
            ValueError: 文件格式错误或数据无效
            FileNotFoundError: 文件不存在
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        try:
            from pyimzml.ImzMLParser import ImzMLParser
        except ImportError:
            raise ImportError("缺少pyimzml库，请安装: pip install pyimzml")
            
        try:
            logger.info(f"正在读取imzML文件: {file_path}")
            
            # 检查文件大小，避免处理过大文件
            file_size = os.path.getsize(file_path)
            if file_size > 500 * 1024 * 1024:  # 500MB
                logger.warning(f"文件较大 ({file_size / 1024 / 1024:.1f}MB)，可能需要较长时间")
            
            # 尝试解析imzML文件
            parser = ImzMLParser(file_path)
            
            # 获取坐标点，使用更安全的方式
            coordinates = []
            total_points = 0
            
            try:
                for idx, (x, y, z) in enumerate(parser.coordinates):
                    coordinates.append((x, y))
                    total_points += 1
                    
                    # 对于大文件，限制读取点数进行快速检测
                    if idx > 2000 and file_size > 100 * 1024 * 1024:  # 100MB以上文件限制读取
                        logger.info(f"大文件快速检测模式，已读取{idx+1}个点")
                        break
                        
            except Exception as coord_error:
                logger.error(f"读取坐标数据时出错: {str(coord_error)}")
                if not coordinates:
                    raise ValueError(f"无法读取坐标数据: {str(coord_error)}")
                else:
                    logger.warning(f"部分坐标读取失败，使用已读取的{len(coordinates)}个点")
            
            if not coordinates:
                raise ValueError("imzML文件中未找到有效坐标数据")
            
            # 计算分辨率
            x_coords = [coord[0] for coord in coordinates]
            y_coords = [coord[1] for coord in coordinates]
            
            min_x, max_x = min(x_coords), max(x_coords)
            min_y, max_y = min(y_coords), max(y_coords)
            
            width = max_x - min_x + 1
            height = max_y - min_y + 1
            
            logger.info(f"成功读取分辨率: {width} x {height}")
            logger.info(f"坐标范围: X({min_x}-{max_x}), Y({min_y}-{max_y})")
            logger.info(f"总计读取{len(coordinates)}个坐标点")
            
            return (width, height)
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"读取imzML文件失败: {error_msg}")
            
            # 提供更详细的错误信息和建议
            if "not well-formed" in error_msg or "invalid token" in error_msg:
                raise ValueError(f"imzML文件格式错误或损坏。建议:\n1. 检查文件是否完整下载\n2. 尝试用其他工具验证文件\n3. 重新生成imzML文件\n原始错误: {error_msg}")
            elif "No such file" in error_msg:
                raise FileNotFoundError(f"找不到相关的.ibd文件，imzML需要配套的.ibd文件")
            else:
                raise ValueError(f"读取imzML文件失败: {error_msg}")
    
    @staticmethod
    def read_mzml_resolution(file_path: str) -> Tuple[int, int]:
        """
        读取mzML文件的分辨率
        注意：mzML格式通常不包含空间信息，此方法返回默认值
        
        Args:
            file_path: mzML文件路径
            
        Returns:
            (width, height): 默认分辨率
        """
        logger.warning(f"mzML格式通常不包含空间分辨率信息，使用默认值")
        return (64, 64)
    

    
    @classmethod
    def read_resolution(cls, file_path: str) -> Tuple[int, int]:
        """
        根据文件扩展名自动选择合适的读取方法
        
        Args:
            file_path: MSI文件路径
            
        Returns:
            (width, height): 图像分辨率
            
        Raises:
            ValueError: 不支持的文件格式
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        file_extension = os.path.splitext(file_path)[1].lower()
        
        if file_extension == '.imzml':
            return cls.read_imzml_resolution(file_path)
        elif file_extension == '.mzml':
            return cls.read_mzml_resolution(file_path)
        else:
            raise ValueError(f"不支持的文件格式: {file_extension}")
    
    @staticmethod
    def validate_resolution(resolution: Tuple[int, int]) -> bool:
        """
        验证分辨率是否合理
        
        Args:
            resolution: (width, height)
            
        Returns:
            bool: 是否有效
        """
        width, height = resolution
        
        # 检查基本条件
        if width <= 0 or height <= 0:
            return False
            
        # 检查是否过大（可能是错误）
        if width > 10000 or height > 10000:
            logger.warning(f"分辨率异常大: {width} x {height}")
            return False
            
        return True

# 便捷函数
def get_msi_resolution(file_path: str) -> Optional[Tuple[int, int]]:
    """
    获取MSI文件分辨率的便捷函数
    
    Args:
        file_path: MSI文件路径
        
    Returns:
        (width, height) 或 None（如果失败）
    """
    try:
        logger.info(f"开始读取文件分辨率: {os.path.basename(file_path)}")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return None
            
        # 检查文件大小
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            logger.error(f"文件为空: {file_path}")
            return None
            
        resolution = MSIReader.read_resolution(file_path)
        
        if MSIReader.validate_resolution(resolution):
            logger.info(f"分辨率读取成功: {resolution[0]} x {resolution[1]}")
            return resolution
        else:
            logger.error(f"分辨率验证失败: {resolution}")
            return None
            
    except ImportError as e:
        logger.error(f"缺少必要的库: {str(e)}")
        return None
    except FileNotFoundError as e:
        logger.error(f"文件错误: {str(e)}")
        return None
    except ValueError as e:
        logger.error(f"文件格式错误: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"未知错误: {str(e)}")
        return None

if __name__ == "__main__":
    # 测试代码
    print("MSI Reader 测试")
    
    # 测试不存在的文件
    try:
        result = get_msi_resolution("nonexistent.imzml")
        print(f"不存在文件测试: {result}")
    except Exception as e:
        print(f"预期错误: {e}")
    
    print("MSI Reader 模块加载完成")