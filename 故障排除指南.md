# MSI Atlas Converter 故障排除指南

## 分辨率识别失败

### 常见问题及解决方案

#### 1. 依赖库问题

**错误信息：** `ImportError: No module named 'pyimzml'`

**解决方案：**
```bash
# 方法1：使用pip安装
pip install pyimzml

# 方法2：使用conda安装
conda install -c bioconda pyimzml

# 方法3：升级到最新版本
pip install --upgrade pyimzml
```

**验证安装：**
```python
import pyimzml
print(pyimzml.__version__)
```

#### 2. imzML文件格式问题

**错误信息：** `not well-formed (invalid token)` 或 `文件格式错误或损坏`

**可能原因：**
- 文件下载不完整或传输过程中损坏
- 文件编码问题
- 缺少配套的.ibd文件
- 文件不是标准的imzML格式

**解决方案：**
1. **重新下载文件**
   - 确保下载完整
   - 检查文件大小是否正确

2. **检查文件完整性**
   ```bash
   # 检查文件是否存在且不为空
   ls -la your_file.imzML
   ls -la your_file.ibd
   ```

3. **验证imzML格式**
   - 使用文本编辑器打开.imzML文件
   - 确认文件开头是XML格式
   - 检查是否有明显的格式错误

4. **确保配套文件存在**
   - imzML文件需要配套的.ibd文件
   - 两个文件必须在同一目录下
   - 文件名（除扩展名外）必须相同

#### 3. 文件路径和权限问题

**错误信息：** `FileNotFoundError` 或 `文件不存在`

**解决方案：**
- 使用绝对路径而非相对路径
- 确保文件路径不包含特殊字符或中文
- 检查文件读取权限
- 避免使用网络驱动器上的文件

#### 4. 大文件处理问题

**现象：** 程序长时间无响应或内存不足

**解决方案：**
- 对于大于500MB的文件，程序会自动启用快速检测模式
- 耐心等待处理完成
- 如果内存不足，尝试关闭其他程序
- 考虑使用较小的测试文件验证功能

#### 5. mzML文件默认分辨率

**现象：** 显示"使用默认分辨率64x64"

**说明：** 这是正常行为，当前版本不支持自动读取这些格式的真实分辨率

**替代方案：**
- 将文件转换为imzML格式
- 手动设置正确的分辨率（见高级故障排除）

### 高级故障排除

#### 启用详细日志

在程序开始处添加以下代码以获得更详细的错误信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### 手动测试MSI读取器

运行测试脚本检查基本功能：

```bash
python test_msi_reader.py
```

#### 检查依赖库版本

```bash
# 检查已安装的库
pip list | grep -i imz
pip list | grep -i numpy
pip list | grep -i pandas

# 检查Python版本
python --version
```

### 性能优化建议

#### 1. 大文件处理

对于大型imzML文件：
- 程序只读取前1000个坐标点进行分辨率检测
- 如果仍然很慢，可以减少这个数量

#### 2. 内存使用

- 关闭其他占用内存的程序
- 对于超大文件，考虑使用64位Python

### 常见错误代码

| 错误信息 | 可能原因 | 解决方案 |
|---------|---------|----------|
| `ImportError: No module named 'pyimzml'` | 缺少pyimzml库 | `pip install pyimzml` |
| `FileNotFoundError` | 文件路径错误 | 检查文件路径和权限 |
| `ValueError: 读取imzML文件失败` | 文件格式问题 | 检查文件完整性和格式 |
| `not well-formed (invalid token)` | XML格式错误 | 文件可能损坏，重新获取文件 |
| `分辨率异常大` | 坐标数据异常 | 检查文件数据的合理性 |

### 获取帮助

如果以上方案都无法解决问题：

1. **收集信息**:
   - Python版本
   - 操作系统版本
   - 文件类型和大小
   - 完整的错误信息

2. **创建最小复现示例**:
   ```python
   from msi_reader import get_msi_resolution
   
   file_path = "your_problematic_file.imzML"
   try:
       result = get_msi_resolution(file_path)
       print(f"成功: {result}")
   except Exception as e:
       print(f"失败: {e}")
       import traceback
       traceback.print_exc()
   ```

3. **联系技术支持**:
   - 提供上述信息
   - 如果可能，提供问题文件的样本

### 预防措施

1. **文件备份**: 在处理重要数据前先备份
2. **环境测试**: 在新环境中先用小文件测试
3. **定期更新**: 保持库的最新版本
4. **文档查阅**: 参考pyimzml官方文档了解支持的格式

### 替代方案

如果自动分辨率读取持续失败：

1. **手动设置分辨率**:
   ```python
   # 在main.py中手动设置
   self.msi_resolution = (your_width, your_height)
   ```

2. **使用其他工具**:
   - MSiReader
   - Cardinal (R包)
   - 其他质谱成像软件

3. **格式转换**:
   - 将数据转换为更标准的imzML格式
   - 使用专业的质谱数据处理软件