#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Label匹配功能
用于验证RGB颜色与标签的对应关系
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import MSIAtlasConverter
import tkinter as tk
from PIL import Image
import numpy as np

def test_label_matching():
    """测试label匹配功能"""
    print("=== Label匹配功能测试 ===")
    
    # 创建应用实例
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    app = MSIAtlasConverter(root)
    
    # 测试加载label文件
    label_file_path = "test data/labels.txt"
    if os.path.exists(label_file_path):
        print(f"\n1. 加载label文件: {label_file_path}")
        app.load_label_file(label_file_path)
        
        if app.label_data:
            print(f"   成功加载 {len(app.label_data)} 个RGB映射")
            
            # 显示前10个映射关系
            print("\n2. 前10个RGB映射关系:")
            count = 0
            for rgb_key, label in app.label_data.items():
                if count < 10:
                    print(f"   {rgb_key} -> {label}")
                    count += 1
                else:
                    break
            
            # 测试具体的RGB值匹配
            print("\n3. 测试具体RGB值匹配:")
            test_rgb_values = [
                (255, 255, 255),  # 白色 - root
                (191, 218, 227),  # 浅蓝色 - Basic cell groups and regions
                (176, 240, 255),  # 蓝色 - Cerebrum
                (0, 0, 0),        # 黑色 - Clear Label
                (100, 150, 200),  # 随机颜色（可能不存在）
            ]
            
            for r, g, b in test_rgb_values:
                label = app.get_label_for_rgb(r, g, b)
                status = "✓" if label else "✗"
                print(f"   RGB({r:3d},{g:3d},{b:3d}) -> {label or 'Unknown'} {status}")
            
            # 测试创建小型测试图像
            print("\n4. 创建测试图像并生成CSV:")
            test_image = np.array([
                [[255, 255, 255], [191, 218, 227]],  # 第一行：白色，浅蓝色
                [[176, 240, 255], [0, 0, 0]]         # 第二行：蓝色，黑色
            ], dtype=np.uint8)
            
            print(f"   测试图像尺寸: {test_image.shape}")
            app.generate_rgb_csv(test_image)
            
            if hasattr(app, 'generated_csv') and app.generated_csv is not None:
                print(f"   生成CSV数据: {len(app.generated_csv)} 行")
                print("\n   CSV数据预览:")
                print(app.generated_csv.to_string(index=False))
                
                # 统计标签匹配情况
                if 'Label' in app.generated_csv.columns:
                    labeled_count = len(app.generated_csv[app.generated_csv['Label'] != 'Unknown'])
                    total_count = len(app.generated_csv)
                    print(f"\n   标签匹配统计: {labeled_count}/{total_count} 个像素点已标注")
                    
                    # 显示各标签的分布
                    label_counts = app.generated_csv['Label'].value_counts()
                    print("\n   标签分布:")
                    for label, count in label_counts.items():
                        print(f"     {label}: {count} 个像素")
            
        else:
            print("   ✗ 加载label文件失败")
    else:
        print(f"   ✗ 找不到label文件: {label_file_path}")
    
    root.destroy()
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_label_matching()